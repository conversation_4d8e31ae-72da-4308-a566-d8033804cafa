import discord
from discord.ext import commands
from discord import app_commands
import os
from datetime import datetime, timedelta
import re
from discord.ui import View, Button

PRICE_FILE = 'data/commodities_prices.txt'
CACHE_TTL = timedelta(hours=6)

class PaginatedView(View):
    def __init__(self, get_embed, total_pages, initial_page=1, timeout=180):
        super().__init__(timeout=timeout)
        self.get_embed = get_embed
        self.total_pages = total_pages
        self.current_page = initial_page
        self.add_item(Button(label="◀", custom_id="prev", style=discord.ButtonStyle.primary, disabled=initial_page == 1))
        self.add_item(Button(label="▶", custom_id="next", style=discord.ButtonStyle.primary, disabled=initial_page == total_pages))

    async def update_buttons(self, interaction: discord.Interaction):
        prev_button = self.children[0]
        next_button = self.children[1]
        prev_button.disabled = self.current_page == 1
        next_button.disabled = self.current_page == self.total_pages
        embed = self.get_embed(self.current_page)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_button_click(self, interaction: discord.Interaction, button: Button):
        if button.custom_id == "prev" and self.current_page > 1:
            self.current_page -= 1
        elif button.custom_id == "next" and self.current_page < self.total_pages:
            self.current_page += 1
        await self.update_buttons(interaction)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        return True

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True

class CommodityPrices(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.last_update = None
        self.load_cache()

    def load_cache(self):
        if os.path.exists(PRICE_FILE):
            self.last_update = datetime.fromtimestamp(os.path.getmtime(PRICE_FILE)).isoformat()
        else:
            self.last_update = None

    def parse_commodities_prices(self):
        """Parse UEX data extract format: single line with ' • ' delimiters"""
        if not os.path.exists(PRICE_FILE):
            return []
        
        try:
            with open(PRICE_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content or content == "invalid_data_input":
                return []
            
            # Split by the UEX delimiter
            items = content.split(" • ")
            
            # Skip the header items (first 3 items are usually metadata)
            # Format: "UEX • Commodities Prices (Average) • Updated Jul 13, 2955 • [data starts here]"
            data_items = items[3:] if len(items) > 3 else items
            
            prices = []
            for item in data_items:
                if ':' in item and 'UEC' in item:
                    try:
                        # Format: "AGRI: Buy 2,041, Sell 2,349 UEC/SCU"
                        commodity, rest = item.split(':', 1)
                        commodity = commodity.strip()
                        
                        # Extract buy and sell prices
                        buy_match = re.search(r'Buy\s+([0-9,]+)', rest)
                        sell_match = re.search(r'Sell\s+([0-9,]+)', rest)
                        
                        if buy_match and sell_match:
                            buy_price = int(buy_match.group(1).replace(',', ''))
                            sell_price = int(sell_match.group(1).replace(',', ''))
                            
                            prices.append({
                                'commodity': commodity,
                                'buy_price': buy_price,
                                'sell_price': sell_price,
                                'avg_price': (buy_price + sell_price) // 2 if buy_price > 0 and sell_price > 0 else max(buy_price, sell_price)
                            })
                    except Exception as e:
                        print(f"Error parsing commodity item '{item}': {e}")
                        continue
            
            return prices
            
        except Exception as e:
            print(f"Error reading commodity prices file: {e}")
            return []

    def format_price(self, price):
        if price is None or price == 0:
            return "N/A"
        if price >= 1_000_000:
            return f"{price // 1_000_000}M UEC"
        elif price >= 1_000:
            return f"{price // 1_000}K UEC"
        else:
            return f"{price} UEC"

    @app_commands.command(name="prices", description="View current commodity prices")
    @app_commands.describe(commodity="Select a commodity from the list")
    async def view_prices(self, interaction: discord.Interaction, commodity: str = None):
        prices = self.parse_commodities_prices()
        if not prices:
            await interaction.response.send_message("No commodity price data available. Please check if the data file is valid.", ephemeral=True)
            return
        if commodity:
            await self.get_price(interaction, commodity)
            return
        sorted_prices = sorted(prices, key=lambda x: x['avg_price'], reverse=True)
        per_page = 25
        total_pages = (len(sorted_prices) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Star Citizen Commodity Prices (Page {page}/{total_pages})",
                description="Current average prices from UEX data extract",
                color=discord.Color.blue()
            )
            for p in sorted_prices[start:end]:
                embed.add_field(
                    name=p['commodity'],
                    value=f"Buy: {self.format_price(p['buy_price'])}\nSell: {self.format_price(p['sell_price'])}",
                    inline=True
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=1)
        embed = get_embed(1)
        message = await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @view_prices.autocomplete('commodity')
    async def prices_commodity_autocomplete(self, interaction: discord.Interaction, current: str):
        prices = self.parse_commodities_prices()
        codes = [p['commodity'] for p in prices]
        current_lower = current.lower()
        filtered = [c for c in codes if current_lower in c.lower()][:25]
        return [app_commands.Choice(name=c, value=c) for c in filtered]

    @app_commands.command(name="price", description="Get price for a specific commodity")
    @app_commands.describe(commodity="Select a commodity from the list")
    async def get_price(self, interaction: discord.Interaction, commodity: str):
        code = commodity.strip().upper()
        prices = self.parse_commodities_prices()
        match = next((p for p in prices if p['commodity'].upper() == code), None)
        
        if not match:
            await interaction.response.send_message(f"Commodity '{commodity}' not found. Use `/prices` to see available commodities.", ephemeral=True)
            return
        
        embed = discord.Embed(
            title=f"Price for {match['commodity']}",
            color=discord.Color.green()
        )
        embed.add_field(name="Buy Price", value=self.format_price(match['buy_price']), inline=True)
        embed.add_field(name="Sell Price", value=self.format_price(match['sell_price']), inline=True)
        embed.add_field(name="Average Price", value=self.format_price(match['avg_price']), inline=True)
        
        if self.last_update:
            embed.set_footer(text=f"Last updated: {self.last_update}")
        
        await interaction.response.send_message(embed=embed)

    @get_price.autocomplete('commodity')
    async def price_commodity_autocomplete(self, interaction: discord.Interaction, current: str):
        prices = self.parse_commodities_prices()
        codes = [p['commodity'] for p in prices]
        current_lower = current.lower()
        filtered = [c for c in codes if current_lower in c.lower()][:25]
        return [app_commands.Choice(name=c, value=c) for c in filtered]

    @app_commands.command(name="update_prices", description="Reload commodity price data from cache")
    @app_commands.checks.has_permissions(administrator=True)
    async def update_prices(self, interaction: discord.Interaction):
        await interaction.response.defer()
        try:
            self.load_cache()
            prices = self.parse_commodities_prices()
            await interaction.followup.send(f"Commodity price data reloaded. Found {len(prices)} commodities.")
        except Exception as e:
            await interaction.followup.send(f"Failed to reload: {e}")

async def setup(bot):
    await bot.add_cog(CommodityPrices(bot)) 