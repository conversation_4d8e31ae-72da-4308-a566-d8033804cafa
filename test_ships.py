#!/usr/bin/env python3
"""
Test to see available ships
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cogs.traderoutes import TradeRoutes
from unittest.mock import MagicMock

async def test_ships():
    """Test available ships"""
    
    # Create a mock bot
    mock_bot = MagicMock()
    
    # Create the TradeRoutes cog
    trade_routes = TradeRoutes(mock_bot)
    
    # Stop the automatic update task to avoid conflicts
    trade_routes.update_data_extract.cancel()
    
    print("Available ships:")
    print("-" * 40)
    
    try:
        ships = trade_routes.parse_vehicles()
        
        # Filter for ships with cargo capacity
        cargo_ships = [ship for ship in ships if ship.get('scu', 0) > 0]
        cargo_ships.sort(key=lambda x: x.get('scu', 0), reverse=True)
        
        print(f"Found {len(cargo_ships)} ships with cargo capacity:")
        
        for ship in cargo_ships[:20]:  # Show top 20
            name = ship.get('name', 'Unknown')
            scu = ship.get('scu', 0)
            print(f"  {name}: {scu} SCU")
            
        # Look for 890 Jump specifically
        jump_890 = next((ship for ship in ships if '890' in ship.get('name', '').lower()), None)
        if jump_890:
            print(f"\n890 Jump found: {jump_890.get('name')} with {jump_890.get('scu', 0)} SCU")
        else:
            print("\n890 Jump not found in ship list")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ships())
