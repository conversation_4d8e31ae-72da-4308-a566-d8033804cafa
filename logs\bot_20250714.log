2025-07-14 00:02:43,063 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:02:43,064 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:02:43,066 [INFO] Loaded cog: embedgen.py
2025-07-14 00:02:43,067 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:02:43,070 [INFO] Loaded cog: giveaway.py
2025-07-14 00:02:43,073 [INFO] Loaded cog: moderation.py
2025-07-14 00:02:43,075 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:02:43,076 [INFO] Loaded cog: rosters.py
2025-07-14 00:02:43,078 [INFO] Loaded cog: shipgame.py
2025-07-14 00:02:43,080 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:02:43,109 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_data_extract.start()
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

2025-07-14 00:02:43,113 [INFO] Loaded cog: voice.py
2025-07-14 00:02:43,115 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:02:43,116 [INFO] Loaded cog: welcome.py
2025-07-14 00:02:43,116 [INFO] logging in using static token
2025-07-14 00:02:43,986 [INFO] Shard ID None has connected to Gateway (Session ID: 53235a05cb24072828a0b7610bf2fbb0).
2025-07-14 00:02:46,010 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:02:46,226 [INFO] Synced 29 slash commands.
2025-07-14 00:03:43,782 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:03:43,785 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:03:43,786 [INFO] Loaded cog: embedgen.py
2025-07-14 00:03:43,788 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:03:43,791 [INFO] Loaded cog: giveaway.py
2025-07-14 00:03:43,793 [INFO] Loaded cog: moderation.py
2025-07-14 00:03:43,794 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:03:43,796 [INFO] Loaded cog: rosters.py
2025-07-14 00:03:43,797 [INFO] Loaded cog: shipgame.py
2025-07-14 00:03:43,797 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:03:43,814 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_uex_data.start()
    ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_uex_data'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_uex_data'

2025-07-14 00:03:43,816 [INFO] Loaded cog: voice.py
2025-07-14 00:03:43,821 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:03:43,822 [INFO] Loaded cog: welcome.py
2025-07-14 00:03:43,822 [INFO] logging in using static token
2025-07-14 00:03:44,532 [INFO] Shard ID None has connected to Gateway (Session ID: 4a686e7a3a6d1921b1f41af53d1c2b02).
2025-07-14 00:03:46,543 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:03:46,796 [INFO] Synced 29 slash commands.
2025-07-14 00:04:43,880 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:04:43,882 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:04:43,883 [INFO] Loaded cog: embedgen.py
2025-07-14 00:04:43,885 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:04:43,887 [INFO] Loaded cog: giveaway.py
2025-07-14 00:04:43,890 [INFO] Loaded cog: moderation.py
2025-07-14 00:04:43,891 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:04:43,892 [INFO] Loaded cog: rosters.py
2025-07-14 00:04:43,893 [INFO] Loaded cog: shipgame.py
2025-07-14 00:04:43,894 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:04:43,910 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_data_extract.start()
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

2025-07-14 00:04:43,912 [INFO] Loaded cog: voice.py
2025-07-14 00:04:43,914 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:04:43,915 [INFO] Loaded cog: welcome.py
2025-07-14 00:04:43,915 [INFO] logging in using static token
2025-07-14 00:04:44,718 [INFO] Shard ID None has connected to Gateway (Session ID: 4a16389f58a0c8ac542c5a9ff60607ea).
2025-07-14 00:04:46,738 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:04:47,051 [INFO] Synced 29 slash commands.
2025-07-14 00:05:55,622 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:05:55,623 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:05:55,625 [INFO] Loaded cog: embedgen.py
2025-07-14 00:05:55,626 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:05:55,629 [INFO] Loaded cog: giveaway.py
2025-07-14 00:05:55,631 [INFO] Loaded cog: moderation.py
2025-07-14 00:05:55,633 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:05:55,634 [INFO] Loaded cog: rosters.py
2025-07-14 00:05:55,636 [INFO] Loaded cog: shipgame.py
2025-07-14 00:05:55,637 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:05:55,656 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 783, in add_cog
    cog = await cog._inject(self, override=override, guild=guild, guilds=guilds)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\cog.py", line 684, in _inject
    await maybe_coroutine(self.cog_load)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\utils.py", line 693, in maybe_coroutine
    return await value
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 241, in cog_load
    await self.update_data_extract()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 368, in __call__
    return await self.coro(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 232, in update_data_extract
    await self.fetch_and_cache_commodities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'

2025-07-14 00:05:55,658 [INFO] Loaded cog: voice.py
2025-07-14 00:05:55,660 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:05:55,660 [INFO] Loaded cog: welcome.py
2025-07-14 00:05:55,660 [INFO] logging in using static token
2025-07-14 00:05:55,665 [ERROR] Unhandled exception in internal background task 'update_data_extract'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 232, in update_data_extract
    await self.fetch_and_cache_commodities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'
2025-07-14 00:05:56,404 [INFO] Shard ID None has connected to Gateway (Session ID: fc2e8a068383a40ea97998683c432470).
2025-07-14 00:05:58,414 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:05:58,813 [INFO] Synced 29 slash commands.
2025-07-14 00:07:38,092 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:07:38,094 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:07:38,095 [INFO] Loaded cog: embedgen.py
2025-07-14 00:07:38,097 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:07:38,100 [INFO] Loaded cog: giveaway.py
2025-07-14 00:07:38,103 [INFO] Loaded cog: moderation.py
2025-07-14 00:07:38,104 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:07:38,105 [INFO] Loaded cog: rosters.py
2025-07-14 00:07:38,107 [INFO] Loaded cog: shipgame.py
2025-07-14 00:07:38,108 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:07:38,121 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:07:38,123 [INFO] Loaded cog: voice.py
2025-07-14 00:07:38,125 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:07:38,126 [INFO] Loaded cog: welcome.py
2025-07-14 00:07:38,126 [INFO] logging in using static token
2025-07-14 00:07:38,892 [INFO] Shard ID None has connected to Gateway (Session ID: a69c183e7ed1f9274b1e40addf3ab11e).
2025-07-14 00:07:40,918 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:07:41,137 [INFO] Synced 29 slash commands.
2025-07-14 00:08:55,988 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:08:55,989 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:08:55,991 [INFO] Loaded cog: embedgen.py
2025-07-14 00:08:55,992 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:08:55,997 [INFO] Loaded cog: giveaway.py
2025-07-14 00:08:56,000 [INFO] Loaded cog: moderation.py
2025-07-14 00:08:56,001 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:08:56,003 [INFO] Loaded cog: rosters.py
2025-07-14 00:08:56,004 [INFO] Loaded cog: shipgame.py
2025-07-14 00:08:56,004 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:08:56,008 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:08:56,009 [INFO] Loaded cog: voice.py
2025-07-14 00:08:56,011 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:08:56,011 [INFO] Loaded cog: welcome.py
2025-07-14 00:08:56,011 [INFO] logging in using static token
2025-07-14 00:08:56,859 [INFO] Shard ID None has connected to Gateway (Session ID: 9c2adf7cd23e7363a724405e595605be).
2025-07-14 00:08:58,876 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:08:59,130 [INFO] Synced 29 slash commands.
2025-07-14 00:10:55,059 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:10:55,060 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:10:55,061 [INFO] Loaded cog: embedgen.py
2025-07-14 00:10:55,064 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:10:55,066 [INFO] Loaded cog: giveaway.py
2025-07-14 00:10:55,071 [INFO] Loaded cog: moderation.py
2025-07-14 00:10:55,072 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:10:55,074 [INFO] Loaded cog: rosters.py
2025-07-14 00:10:55,075 [INFO] Loaded cog: shipgame.py
2025-07-14 00:10:55,076 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:10:55,089 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:10:55,091 [INFO] Loaded cog: voice.py
2025-07-14 00:10:55,093 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:10:55,093 [INFO] Loaded cog: welcome.py
2025-07-14 00:10:55,094 [INFO] logging in using static token
2025-07-14 00:10:55,988 [INFO] Shard ID None has connected to Gateway (Session ID: 66714dd0b343cd97125b64580d51d80c).
2025-07-14 00:10:58,002 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:10:58,232 [INFO] Synced 29 slash commands.
2025-07-14 00:13:00,574 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:13:00,576 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:13:00,577 [INFO] Loaded cog: embedgen.py
2025-07-14 00:13:00,578 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:13:00,581 [INFO] Loaded cog: giveaway.py
2025-07-14 00:13:00,583 [INFO] Loaded cog: moderation.py
2025-07-14 00:13:00,584 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:13:00,586 [INFO] Loaded cog: rosters.py
2025-07-14 00:13:00,587 [INFO] Loaded cog: shipgame.py
2025-07-14 00:13:00,588 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:13:00,591 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:13:00,593 [INFO] Loaded cog: voice.py
2025-07-14 00:13:00,595 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:13:00,595 [INFO] Loaded cog: welcome.py
2025-07-14 00:13:00,595 [INFO] logging in using static token
2025-07-14 00:13:02,849 [INFO] Shard ID None has connected to Gateway (Session ID: 59003e59c259dd8f8517e19ae720b587).
2025-07-14 00:13:04,867 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:13:05,140 [INFO] Synced 29 slash commands globally.
2025-07-14 00:13:05,140 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:15:00,752 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:15:00,754 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:15:00,756 [INFO] Loaded cog: embedgen.py
2025-07-14 00:15:00,757 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:15:00,760 [INFO] Loaded cog: giveaway.py
2025-07-14 00:15:00,763 [INFO] Loaded cog: moderation.py
2025-07-14 00:15:00,767 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:15:00,769 [INFO] Loaded cog: rosters.py
2025-07-14 00:15:00,771 [INFO] Loaded cog: shipgame.py
2025-07-14 00:15:00,772 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:15:00,784 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:15:00,786 [INFO] Loaded cog: voice.py
2025-07-14 00:15:00,788 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:15:00,788 [INFO] Loaded cog: welcome.py
2025-07-14 00:15:00,788 [INFO] logging in using static token
2025-07-14 00:15:01,688 [INFO] Shard ID None has connected to Gateway (Session ID: b064717cb48878e7d451f110bba4f01b).
2025-07-14 00:15:03,704 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:15:04,104 [INFO] Synced 31 slash commands globally.
2025-07-14 00:15:04,105 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:16:54,552 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:16:54,554 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:16:54,555 [INFO] Loaded cog: embedgen.py
2025-07-14 00:16:54,556 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:16:54,560 [INFO] Loaded cog: giveaway.py
2025-07-14 00:16:54,562 [INFO] Loaded cog: moderation.py
2025-07-14 00:16:54,563 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:16:54,565 [INFO] Loaded cog: rosters.py
2025-07-14 00:16:54,566 [INFO] Loaded cog: shipgame.py
2025-07-14 00:16:54,567 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:16:54,581 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:16:54,582 [INFO] Loaded cog: voice.py
2025-07-14 00:16:54,584 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:16:54,584 [INFO] Loaded cog: welcome.py
2025-07-14 00:16:54,584 [INFO] logging in using static token
2025-07-14 00:16:55,266 [INFO] Shard ID None has connected to Gateway (Session ID: 042c1de51c64e1b2aeb2254566ae70d3).
2025-07-14 00:16:57,275 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:16:57,526 [INFO] Synced 31 slash commands globally.
2025-07-14 00:16:57,526 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:18:34,291 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:18:34,292 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:18:34,294 [INFO] Loaded cog: embedgen.py
2025-07-14 00:18:34,295 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:18:34,298 [INFO] Loaded cog: giveaway.py
2025-07-14 00:18:34,300 [INFO] Loaded cog: moderation.py
2025-07-14 00:18:34,301 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:18:34,303 [INFO] Loaded cog: rosters.py
2025-07-14 00:18:34,304 [INFO] Loaded cog: shipgame.py
2025-07-14 00:18:34,305 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:18:34,318 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:18:34,319 [INFO] Loaded cog: voice.py
2025-07-14 00:18:34,322 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:18:34,322 [INFO] Loaded cog: welcome.py
2025-07-14 00:18:34,322 [INFO] logging in using static token
2025-07-14 00:18:35,094 [INFO] Shard ID None has connected to Gateway (Session ID: 27558d3bdd4aadc5a4e72362496c1f47).
2025-07-14 00:18:37,121 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:18:37,359 [INFO] Synced 31 slash commands globally.
2025-07-14 00:18:37,360 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:20:13,252 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:20:13,254 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:20:13,255 [INFO] Loaded cog: embedgen.py
2025-07-14 00:20:13,256 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:20:13,260 [INFO] Loaded cog: giveaway.py
2025-07-14 00:20:13,262 [INFO] Loaded cog: moderation.py
2025-07-14 00:20:13,263 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:20:13,268 [INFO] Loaded cog: rosters.py
2025-07-14 00:20:13,269 [INFO] Loaded cog: shipgame.py
2025-07-14 00:20:13,270 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:20:13,284 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:20:13,285 [INFO] Loaded cog: voice.py
2025-07-14 00:20:13,287 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:20:13,288 [INFO] Loaded cog: welcome.py
2025-07-14 00:20:13,288 [INFO] logging in using static token
2025-07-14 00:20:15,281 [INFO] Shard ID None has connected to Gateway (Session ID: 9e18abb38a7ebda40763144a55b4f397).
2025-07-14 00:20:17,307 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:20:17,499 [INFO] Synced 31 slash commands globally.
2025-07-14 00:20:17,499 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:24:19,729 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:24:19,730 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:24:19,731 [INFO] Loaded cog: embedgen.py
2025-07-14 00:24:19,733 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:24:19,736 [INFO] Loaded cog: giveaway.py
2025-07-14 00:24:19,738 [INFO] Loaded cog: moderation.py
2025-07-14 00:24:19,739 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:24:19,741 [INFO] Loaded cog: rosters.py
2025-07-14 00:24:19,742 [INFO] Loaded cog: shipgame.py
2025-07-14 00:24:19,744 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:24:19,756 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:24:19,757 [INFO] Loaded cog: voice.py
2025-07-14 00:24:19,759 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:24:19,759 [INFO] Loaded cog: welcome.py
2025-07-14 00:24:19,759 [INFO] logging in using static token
2025-07-14 00:24:20,489 [INFO] Shard ID None has connected to Gateway (Session ID: 74b4f629f14979abad520a44cb64504e).
2025-07-14 00:24:22,504 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:24:22,799 [INFO] Synced 31 slash commands globally.
2025-07-14 00:24:22,799 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:27:31,875 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:27:31,877 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:27:31,878 [INFO] Loaded cog: embedgen.py
2025-07-14 00:27:31,880 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:27:31,883 [INFO] Loaded cog: giveaway.py
2025-07-14 00:27:31,886 [INFO] Loaded cog: moderation.py
2025-07-14 00:27:31,887 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:27:31,890 [INFO] Loaded cog: rosters.py
2025-07-14 00:27:31,891 [INFO] Loaded cog: shipgame.py
2025-07-14 00:27:31,891 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:27:31,905 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:27:31,907 [INFO] Loaded cog: voice.py
2025-07-14 00:27:31,909 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:27:31,909 [INFO] Loaded cog: welcome.py
2025-07-14 00:27:31,909 [INFO] logging in using static token
2025-07-14 00:27:32,674 [INFO] Shard ID None has connected to Gateway (Session ID: 2f31ff44564ebabbff08a4f89ed5a28f).
2025-07-14 00:27:34,692 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:27:34,939 [INFO] Synced 31 slash commands globally.
2025-07-14 00:27:34,939 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
