#!/usr/bin/env python3
"""
Test script to verify UEX data parsing
"""

import sys
import os
import re
sys.path.append('.')

def parse_commodities_prices():
    """Parse UEX data extract format for commodity prices"""
    price_file = 'data/commodities_prices.txt'
    if not os.path.exists(price_file):
        return []
    
    try:
        with open(price_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content or content == "invalid_data_input":
            return []
        
        items = content.split(" • ")
        data_items = items[3:] if len(items) > 3 else items
        
        prices = []
        for item in data_items:
            if ':' in item and 'UEC' in item:
                try:
                    commodity, rest = item.split(':', 1)
                    commodity = commodity.strip()
                    
                    buy_match = re.search(r'Buy\s+([0-9,]+)', rest)
                    sell_match = re.search(r'Sell\s+([0-9,]+)', rest)
                    
                    if buy_match and sell_match:
                        buy_price = int(buy_match.group(1).replace(',', ''))
                        sell_price = int(sell_match.group(1).replace(',', ''))
                        
                        prices.append({
                            'commodity': commodity,
                            'buy_price': buy_price,
                            'sell_price': sell_price,
                            'avg_price': (buy_price + sell_price) // 2 if buy_price > 0 and sell_price > 0 else max(buy_price, sell_price)
                        })
                except Exception as e:
                    print(f"Error parsing commodity item '{item}': {e}")
                    continue
        
        return prices
        
    except Exception as e:
        print(f"Error reading commodity prices file: {e}")
        return []

def parse_vehicles():
    """Parse UEX data extract format for vehicles/ships"""
    vehicles_file = 'data/vehicles.txt'
    if not os.path.exists(vehicles_file):
        return []
    
    try:
        with open(vehicles_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content or content == "invalid_data_input":
            return []
        
        items = content.split(" • ")
        data_items = items[3:] if len(items) > 3 else items
        
        ships = []
        for item in data_items:
            if ':' in item and 'SCU' in item:
                try:
                    name, rest = item.split(':', 1)
                    name = name.strip()
                    
                    scu_match = re.search(r'(\d+)\s+SCU', rest)
                    class_match = re.search(r'Class\s+(\d+)', rest)
                    
                    if scu_match:
                        scu = int(scu_match.group(1))
                        ship_class = int(class_match.group(1)) if class_match else 1
                        
                        ships.append({
                            'name': name,
                            'scu': scu,
                            'class': ship_class
                        })
                except Exception as e:
                    print(f"Error parsing ship item '{item}': {e}")
                    continue
        
        return ships
        
    except Exception as e:
        print(f"Error reading vehicles file: {e}")
        return []

def parse_routes():
    """Parse UEX data extract format for trade routes"""
    routes_file = 'data/commodities_routes.txt'
    if not os.path.exists(routes_file):
        return []
    
    try:
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content or content == "invalid_data_input":
            return []
        
        items = content.split(" • ")
        data_items = items[3:] if len(items) > 3 else items
        
        routes = []
        for item in data_items:
            if ':' in item and '▶' in item and 'UEC' in item:
                try:
                    commodity, rest = item.split(':', 1)
                    commodity = commodity.strip()
                    
                    route_part, profit_part = rest.split('=', 1)
                    buy_location, sell_location = route_part.split('▶')
                    
                    profit_match = re.search(r'Up to\s+([0-9,]+)([MK])?\s+UEC', profit_part)
                    if profit_match:
                        profit_amount = int(profit_match.group(1).replace(',', ''))
                        if profit_match.group(2) == 'M':
                            profit_amount *= 1_000_000
                        elif profit_match.group(2) == 'K':
                            profit_amount *= 1_000
                        
                        routes.append({
                            'commodity': commodity,
                            'buy_location': buy_location.strip(),
                            'sell_location': sell_location.strip(),
                            'profit': profit_amount
                        })
                except Exception as e:
                    print(f"Error parsing route item '{item}': {e}")
                    continue
        
        return routes
        
    except Exception as e:
        print(f"Error reading routes file: {e}")
        return []

def parse_locations():
    """Parse UEX data extract format for locations"""
    locations_file = 'data/locations.txt'
    if not os.path.exists(locations_file):
        return []
    
    try:
        with open(locations_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content or content == "invalid_data_input":
            return []
        
        items = content.split(" • ")
        data_items = items[3:] if len(items) > 3 else items
        
        locations = []
        for item in data_items:
            if item.strip():
                name = item.strip()
                loc_type = 'Unknown'
                
                if '(' in name and ')' in name:
                    loc_type = name[name.find('(')+1:name.find(')')]
                    name = name[:name.find('(')].strip()
                
                locations.append({
                    'name': name,
                    'type': loc_type
                })
        
        return locations
        
    except Exception as e:
        print(f"Error reading locations file: {e}")
        return []

def test_parsing():
    print("Testing UEX data parsing...")
    
    # Test commodity prices parsing
    print("\n=== Testing Commodity Prices ===")
    prices = parse_commodities_prices()
    print(f"Found {len(prices)} commodities")
    if prices:
        print("Sample commodities:")
        for p in prices[:5]:
            print(f"  {p['commodity']}: Buy {p['buy_price']}, Sell {p['sell_price']}")
    
    # Test vehicles parsing
    print("\n=== Testing Vehicles/Ships ===")
    ships = parse_vehicles()
    print(f"Found {len(ships)} ships")
    if ships:
        print("Sample ships:")
        for s in ships[:5]:
            print(f"  {s['name']}: {s['scu']} SCU, Class {s['class']}")
    
    # Test routes parsing
    print("\n=== Testing Trade Routes ===")
    routes = parse_routes()
    print(f"Found {len(routes)} routes")
    if routes:
        print("Sample routes:")
        for r in routes[:5]:
            print(f"  {r['commodity']}: {r['buy_location']} ▶ {r['sell_location']} = {r['profit']} UEC")
    
    # Test locations parsing
    print("\n=== Testing Locations ===")
    locations = parse_locations()
    print(f"Found {len(locations)} locations")
    if locations:
        print("Sample locations:")
        for l in locations[:5]:
            print(f"  {l['name']} ({l['type']})")
    
    print("\n=== Parsing Test Complete ===")

if __name__ == "__main__":
    test_parsing() 