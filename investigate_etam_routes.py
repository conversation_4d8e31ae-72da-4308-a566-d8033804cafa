#!/usr/bin/env python3
"""
Investigate ETAM routes specifically to match the screenshot
"""

import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

UEX_API_BASE = "https://api.uexcorp.space/2.0"
UEX_API_TOKEN = os.getenv('UEX_API_TOKEN')

async def investigate_etam():
    """Investigate ETAM routes specifically"""
    headers = {}
    if UEX_API_TOKEN:
        headers['Authorization'] = f'Bearer {UEX_API_TOKEN}'
    
    print("Investigating ETAM routes to match screenshot...")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        
        # Get ETAM routes with 2M investment
        etam_id = 29  # From previous investigation
        url = f"{UEX_API_BASE}/commodities_routes?id_commodity={etam_id}&investment=2000000"
        
        try:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, dict) and 'data' in data:
                        routes = data['data']
                        print(f"Found {len(routes)} ETAM routes with 2M investment")
                        
                        # Sort by profit descending
                        routes.sort(key=lambda x: x.get('profit', 0), reverse=True)
                        
                        print(f"\nTop 10 ETAM routes:")
                        for i, route in enumerate(routes[:10], 1):
                            origin = route.get('origin_terminal_name', 'Unknown')
                            dest = route.get('destination_terminal_name', 'Unknown')
                            profit = route.get('profit', 0)
                            investment = route.get('investment', 0)
                            price_origin = route.get('price_origin', 0)
                            price_dest = route.get('price_destination', 0)
                            
                            print(f"{i:2d}. {origin} → {dest}")
                            print(f"    Profit: {profit:,.0f} UEC, Investment: {investment:,.0f} UEC")
                            print(f"    Buy: {price_origin:.2f}, Sell: {price_dest:.2f}")
                            
                            # Look for routes matching the screenshot
                            if 'golden river' in origin.lower() and 'samson' in dest.lower():
                                print(f"    *** MATCHES SCREENSHOT! ***")
                            print()
                        
                        # Look specifically for routes with "Golden River" and "Samson"
                        print(f"\nLooking for routes matching screenshot:")
                        matching_routes = []
                        for route in routes:
                            origin = route.get('origin_terminal_name', '').lower()
                            dest = route.get('destination_terminal_name', '').lower()
                            
                            if ('golden' in origin and 'river' in origin) or 'samson' in dest:
                                matching_routes.append(route)
                        
                        if matching_routes:
                            print(f"Found {len(matching_routes)} potentially matching routes:")
                            for route in matching_routes:
                                origin = route.get('origin_terminal_name', 'Unknown')
                                dest = route.get('destination_terminal_name', 'Unknown')
                                profit = route.get('profit', 0)
                                investment = route.get('investment', 0)
                                print(f"  {origin} → {dest} (Profit: {profit:,.0f}, Investment: {investment:,.0f})")
                        else:
                            print("No routes found matching screenshot patterns")
                            
                        # Also check if we need to filter by cargo capacity (388 SCU for 890 Jump)
                        print(f"\nFiltering by 890 Jump cargo capacity (388 SCU):")
                        cargo_filtered = []
                        for route in routes:
                            investment = route.get('investment', 0)
                            price_origin = route.get('price_origin', 0)
                            if price_origin > 0:
                                scu_needed = investment / price_origin
                                if scu_needed <= 388:  # 890 Jump cargo capacity
                                    cargo_filtered.append(route)
                        
                        cargo_filtered.sort(key=lambda x: x.get('profit', 0), reverse=True)
                        print(f"Found {len(cargo_filtered)} routes that fit in 890 Jump")
                        
                        if cargo_filtered:
                            print(f"\nTop 5 routes that fit in 890 Jump:")
                            for i, route in enumerate(cargo_filtered[:5], 1):
                                origin = route.get('origin_terminal_name', 'Unknown')
                                dest = route.get('destination_terminal_name', 'Unknown')
                                profit = route.get('profit', 0)
                                investment = route.get('investment', 0)
                                price_origin = route.get('price_origin', 0)
                                scu_needed = investment / price_origin if price_origin > 0 else 0
                                
                                print(f"{i}. {origin} → {dest}")
                                print(f"   Profit: {profit:,.0f} UEC, Investment: {investment:,.0f} UEC")
                                print(f"   SCU needed: {scu_needed:.0f}")
                                
                else:
                    text = await response.text()
                    print(f"Error: {text}")
                    
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(investigate_etam())
