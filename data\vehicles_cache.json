{"vehicles": [{"name": "100i", "scu": 2, "class": 1, "is_cargo": false}, {"name": "125a", "scu": 2, "class": 1, "is_cargo": false}, {"name": "135c", "scu": 6, "class": 2, "is_cargo": true}, {"name": "300i", "scu": 8, "class": 1, "is_cargo": false}, {"name": "315p", "scu": 12, "class": 2, "is_cargo": true}, {"name": "325a", "scu": 4, "class": 1, "is_cargo": false}, {"name": "350r", "scu": 4, "class": 1, "is_cargo": false}, {"name": "400i", "scu": 42, "class": 1, "is_cargo": false}, {"name": "600i Executive Edition", "scu": 40, "class": 1, "is_cargo": false}, {"name": "600i Explorer", "scu": 44, "class": 1, "is_cargo": false}, {"name": "600i Touring", "scu": 20, "class": 1, "is_cargo": false}, {"name": "85X", "scu": 0, "class": 1, "is_cargo": false}, {"name": "890 Jump", "scu": 388, "class": 1, "is_cargo": false}, {"name": "A2 Hercules Starlifter", "scu": 234, "class": 1, "is_cargo": false}, {"name": "Apollo Medivac", "scu": 28, "class": 1, "is_cargo": false}, {"name": "Apollo Triage", "scu": 28, "class": 1, "is_cargo": false}, {"name": "Ares Inferno Starfighter", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Ares Ion Starfighter", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Arrastra", "scu": 576, "class": 1, "is_cargo": false}, {"name": "Arrow", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Aurora CL", "scu": 6, "class": 2, "is_cargo": true}, {"name": "Aurora ES", "scu": 3, "class": 1, "is_cargo": false}, {"name": "Aurora LN", "scu": 3, "class": 1, "is_cargo": false}, {"name": "Aurora LX", "scu": 3, "class": 1, "is_cargo": false}, {"name": "Aurora MR", "scu": 3, "class": 1, "is_cargo": false}, {"name": "<PERSON>nger Stalker", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Avenger Titan", "scu": 8, "class": 2, "is_cargo": true}, {"name": "Avenger Titan Renegade", "scu": 8, "class": 1, "is_cargo": false}, {"name": "Avenger Warlock", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Blade", "scu": 0, "class": 1, "is_cargo": false}, {"name": "B<PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "C2 Hercules Starlifter", "scu": 696, "class": 4, "is_cargo": true}, {"name": "C8 Pisces", "scu": 4, "class": 1, "is_cargo": false}, {"name": "C8R Pisces Rescue", "scu": 0, "class": 1, "is_cargo": false}, {"name": "C8X Pisces Expedition", "scu": 4, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON>", "scu": 456, "class": 1, "is_cargo": false}, {"name": "Carrack Expedition", "scu": 456, "class": 1, "is_cargo": false}, {"name": "Caterpillar", "scu": 576, "class": 4, "is_cargo": true}, {"name": "Caterpillar Best In Show Edition", "scu": 576, "class": 4, "is_cargo": true}, {"name": "Caterpillar Pirate Edition", "scu": 576, "class": 4, "is_cargo": true}, {"name": "Constellation Andromeda", "scu": 96, "class": 1, "is_cargo": false}, {"name": "Constellation Aquila", "scu": 96, "class": 1, "is_cargo": false}, {"name": "Constellation Phoenix", "scu": 80, "class": 1, "is_cargo": false}, {"name": "Constellation Phoenix Emerald", "scu": 80, "class": 1, "is_cargo": false}, {"name": "Constellation Taurus", "scu": 174, "class": 4, "is_cargo": true}, {"name": "Corsair", "scu": 72, "class": 1, "is_cargo": false}, {"name": "Crucible", "scu": 240, "class": 1, "is_cargo": false}, {"name": "Cut<PERSON> Black", "scu": 46, "class": 1, "is_cargo": false}, {"name": "Cutlass Black Best In Show Edition", "scu": 46, "class": 2, "is_cargo": true}, {"name": "Cutlass Blue", "scu": 12, "class": 1, "is_cargo": false}, {"name": "Cutlass Red", "scu": 12, "class": 1, "is_cargo": false}, {"name": "Cutlass Steel", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Cutter", "scu": 4, "class": 2, "is_cargo": true}, {"name": "Cutter Scout", "scu": 2, "class": 1, "is_cargo": false}, {"name": "Cutter Rambler", "scu": 2, "class": 1, "is_cargo": false}, {"name": "Defender", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Dragonfly Black", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Dragonfly Yellowjacket", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Eclipse", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Endeavor", "scu": 500, "class": 1, "is_cargo": false}, {"name": "Expanse", "scu": 128, "class": 1, "is_cargo": false}, {"name": "F7A Hornet Mk I", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C Hornet Mk II", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C Hornet Wildfire Mk I", "scu": 2, "class": 1, "is_cargo": false}, {"name": "F7C-M Super Hornet Mk I", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C-M Super Hornet Heartseeker Mk I", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C-R Hornet Tracker Mk I", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C-S Hornet Ghost Mk I", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F8C Lightning", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Freelancer", "scu": 66, "class": 3, "is_cargo": true}, {"name": "Freelancer DUR", "scu": 36, "class": 1, "is_cargo": false}, {"name": "Freelancer MAX", "scu": 120, "class": 4, "is_cargo": true}, {"name": "Freelancer MIS", "scu": 36, "class": 1, "is_cargo": false}, {"name": "Fury", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Fury LX", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Fury MX", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Galaxy", "scu": 64, "class": 3, "is_cargo": true}, {"name": "Genesis Starliner", "scu": 300, "class": 1, "is_cargo": false}, {"name": "Gladiator", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Gladius Pirate", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Glaive", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Hammerhead", "scu": 40, "class": 1, "is_cargo": false}, {"name": "Hammerhead Best In Show Edition", "scu": 40, "class": 1, "is_cargo": false}, {"name": "Hawk", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Herald", "scu": 0, "class": 1, "is_cargo": false}, {"name": "HoverQuad", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Hull A", "scu": 64, "class": 3, "is_cargo": true}, {"name": "Hull B", "scu": 384, "class": 4, "is_cargo": true}, {"name": "Hull C", "scu": 4608, "class": 4, "is_cargo": true}, {"name": "Hull D", "scu": 20736, "class": 4, "is_cargo": true}, {"name": "Hull E", "scu": 98304, "class": 4, "is_cargo": true}, {"name": "Hurricane", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON>dris<PERSON><PERSON>", "scu": 1168, "class": 1, "is_cargo": false}, {"name": "Idris-P", "scu": 1374, "class": 1, "is_cargo": false}, {"name": "Javelin", "scu": 5400, "class": 1, "is_cargo": false}, {"name": "Khartu-al", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON>", "scu": 3792, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON> Privateer", "scu": 768, "class": 1, "is_cargo": false}, {"name": "Legionnaire", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Liberator", "scu": 400, "class": 5, "is_cargo": false}, {"name": "M2 Hercules Starlifter", "scu": 522, "class": 4, "is_cargo": true}, {"name": "M50", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Mantis", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Merchantman", "scu": 2880, "class": 4, "is_cargo": true}, {"name": "Mercury Star Runner", "scu": 114, "class": 1, "is_cargo": false}, {"name": "MOLE", "scu": 96, "class": 1, "is_cargo": false}, {"name": "MOLE Carbon Edition", "scu": 96, "class": 1, "is_cargo": false}, {"name": "MOLE Talus Edition", "scu": 96, "class": 1, "is_cargo": false}, {"name": "MPUV Cargo", "scu": 2, "class": 2, "is_cargo": true}, {"name": "MPUV Personnel", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Mustang Alpha", "scu": 4, "class": 1, "is_cargo": false}, {"name": "Mustang Alpha Vindicator", "scu": 4, "class": 2, "is_cargo": true}, {"name": "Mustang Beta", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Mustang Delta", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Mustang Gamma", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Mustang Omega", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Na<PERSON><PERSON>", "scu": 64, "class": 1, "is_cargo": false}, {"name": "Nautilus Solstice Edition", "scu": 64, "class": 1, "is_cargo": false}, {"name": "Nomad", "scu": 24, "class": 2, "is_cargo": true}, {"name": "Nox", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Odyssey", "scu": 252, "class": 1, "is_cargo": false}, {"name": "Orion", "scu": 384, "class": 1, "is_cargo": false}, {"name": "P-52 Merlin", "scu": 0, "class": 1, "is_cargo": false}, {"name": "P-72 <PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "P-72 <PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Per<PERSON><PERSON>", "scu": 50, "class": 1, "is_cargo": false}, {"name": "Pioneer", "scu": 1000, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON>", "scu": 576, "class": 1, "is_cargo": false}, {"name": "Prospector", "scu": 32, "class": 1, "is_cargo": false}, {"name": "Prowler", "scu": 0, "class": 1, "is_cargo": false}, {"name": "RAFT", "scu": 192, "class": 4, "is_cargo": true}, {"name": "Railen", "scu": 320, "class": 4, "is_cargo": true}, {"name": "Razor", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Razor EX", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Razor LX", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Reclaimer", "scu": 420, "class": 1, "is_cargo": false}, {"name": "Reclaimer Best In Show Edition", "scu": 180, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON>", "scu": 2, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON>", "scu": 6, "class": 2, "is_cargo": true}, {"name": "<PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON>", "scu": 2, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON>", "scu": 1, "class": 1, "is_cargo": false}, {"name": "Retaliator", "scu": 74, "class": 3, "is_cargo": true}, {"name": "Retaliator Bomber", "scu": 74, "class": 1, "is_cargo": false}, {"name": "Sabre", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Sabre Comet", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Sabre Raven", "scu": 0, "class": 1, "is_cargo": false}, {"name": "San tok.Yāi", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "A1 Spirit", "scu": 0, "class": 1, "is_cargo": false}, {"name": "C1 Spirit", "scu": 64, "class": 3, "is_cargo": true}, {"name": "E1 Spirit", "scu": 0, "class": 1, "is_cargo": false}, {"name": "SRV", "scu": 12, "class": 1, "is_cargo": false}, {"name": "Starfarer", "scu": 291, "class": 1, "is_cargo": false}, {"name": "Starfarer Gemini", "scu": 291, "class": 1, "is_cargo": false}, {"name": "Syulen", "scu": 6, "class": 2, "is_cargo": true}, {"name": "Talon", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Talon Shrike", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Valkyrie", "scu": 90, "class": 1, "is_cargo": false}, {"name": "Valkyrie Liberator Edition", "scu": 30, "class": 1, "is_cargo": false}, {"name": "<PERSON> Ha<PERSON>inger", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Vanguard Hoplite", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Vanguard Sentinel", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Vanguard Warden", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Vulcan", "scu": 12, "class": 1, "is_cargo": false}, {"name": "Vulture", "scu": 12, "class": 1, "is_cargo": false}, {"name": "Zeus Mk II ES", "scu": 32, "class": 2, "is_cargo": true}, {"name": "Zeus Mk II MR", "scu": 16, "class": 1, "is_cargo": false}, {"name": "Zeus Mk II CL", "scu": 128, "class": 4, "is_cargo": true}, {"name": "F8C Lightning Executive Edition", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7A Hornet Mk II", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C Hornet Mk I", "scu": 2, "class": 1, "is_cargo": false}, {"name": "MPUV Tractor", "scu": 16, "class": 1, "is_cargo": false}, {"name": "Sabre Firebird", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Ironclad", "scu": 1536, "class": 4, "is_cargo": true}, {"name": "Ironclad Assault", "scu": 1152, "class": 4, "is_cargo": true}, {"name": "Sabre Peregrine", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Fortune", "scu": 12, "class": 1, "is_cargo": false}, {"name": "Intrepid", "scu": 8, "class": 2, "is_cargo": true}, {"name": "Guardian", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Guardian QI", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Starlancer TAC", "scu": 96, "class": 1, "is_cargo": false}, {"name": "Starlancer MAX", "scu": 224, "class": 4, "is_cargo": true}, {"name": "Starlancer BLD", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C-R Hornet Tracker Mk II", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C-S Hornet Ghost Mk II", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON>pin Medic", "scu": 0, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON><PERSON>", "scu": 0, "class": 1, "is_cargo": false}, {"name": "F7C-M Super Hornet Mk II", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Golem", "scu": 32, "class": 1, "is_cargo": false}, {"name": "<PERSON><PERSON>", "scu": 180, "class": 5, "is_cargo": true}, {"name": "Guardian MX", "scu": 0, "class": 1, "is_cargo": false}, {"name": "Prowler Utility", "scu": 32, "class": 2, "is_cargo": true}], "last_update": "2025-07-14T03:17:16.189072+00:00"}