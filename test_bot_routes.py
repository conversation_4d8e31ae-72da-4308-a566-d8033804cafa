#!/usr/bin/env python3
"""
Test the bot's new route fetching functionality
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cogs.traderoutes import TradeRoutes
from unittest.mock import MagicMock

async def test_bot_routes():
    """Test the bot's route fetching"""
    
    # Create a mock bot
    mock_bot = MagicMock()
    
    # Create the TradeRoutes cog
    trade_routes = TradeRoutes(mock_bot)
    
    # Stop the automatic update task to avoid conflicts
    trade_routes.update_data_extract.cancel()
    
    print("Testing bot's route fetching for 890 Jump with 2M investment...")
    print("-" * 60)
    
    # Test parameters matching the screenshot
    investment = 2000000  # 2M UEC
    max_cargo = 388  # 890 Jump cargo capacity
    
    try:
        # Fetch routes using the bot's method
        routes = await trade_routes.fetch_routes_for_investment(investment, max_cargo)
        
        print(f"Found {len(routes)} routes for {investment:,} UEC investment and {max_cargo} SCU cargo")
        
        if routes:
            print("\nTop 5 routes:")
            for i, route in enumerate(routes[:5], 1):
                commodity_name = route.get('commodity_name', 'Unknown')
                origin = route.get('origin_terminal_name', 'Unknown')
                destination = route.get('destination_terminal_name', 'Unknown')
                profit = route.get('profit', 0)
                investment_needed = route.get('investment', 0)
                price_origin = route.get('price_origin', 0)
                price_destination = route.get('price_destination', 0)
                distance = route.get('distance', 0)
                
                # Calculate quantity and actual profit based on our investment
                if price_origin > 0:
                    max_quantity_by_investment = min(investment_needed, investment) / price_origin
                    max_quantity_by_cargo = max_cargo
                    actual_quantity = min(max_quantity_by_investment, max_quantity_by_cargo)
                    
                    actual_cost = actual_quantity * price_origin
                    actual_revenue = actual_quantity * price_destination
                    actual_profit = actual_revenue - actual_cost
                    roi = (actual_profit / actual_cost) * 100 if actual_cost > 0 else 0
                    
                    print(f"\n#{i} {commodity_name}")
                    print(f"  Buy: {origin}")
                    print(f"  Sell: {destination}")
                    print(f"  Quantity: {actual_quantity:.0f} SCU")
                    print(f"  Cost: {actual_cost:,.0f} UEC")
                    print(f"  Revenue: {actual_revenue:,.0f} UEC")
                    print(f"  Profit: {actual_profit:,.0f} UEC ({roi:.1f}% ROI)")
                    if distance and distance > 0:
                        print(f"  Distance: {distance/1000000:.0f}km")
        else:
            print("No routes found!")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_bot_routes())
