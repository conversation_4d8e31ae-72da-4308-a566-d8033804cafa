#!/usr/bin/env python3
"""
Test script to fetch real data from UEX API
"""

import asyncio
import aiohttp
import json
from datetime import datetime

UEX_API_BASE = "https://api.uexcorp.space/2.0"

async def test_fetch_vehicles():
    """Test fetching vehicles from UEX API"""
    url = f"{UEX_API_BASE}/vehicles"
    print(f"Fetching vehicles from: {url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    response_data = await response.json()
                    
                    # Handle the response format: {"status": "ok", "data": [...]}
                    if isinstance(response_data, dict) and 'data' in response_data:
                        vehicles_data = response_data['data']
                    else:
                        vehicles_data = response_data
                    
                    print(f"Successfully fetched {len(vehicles_data)} vehicles")
                    
                    # Convert to UEX format for consistency
                    formatted_data = ["UEX", "Ships and Vehicles", f"Updated {datetime.now().strftime('%b %d, %Y')}"]
                    
                    spaceships = 0
                    for vehicle in vehicles_data:
                        if isinstance(vehicle, dict) and vehicle.get('is_spaceship', 0) == 1:  # Only spaceships
                            name = vehicle.get('name', 'Unknown')
                            scu = vehicle.get('scu', 0)
                            ship_class = get_ship_class(vehicle)
                            formatted_data.append(f"{name}: {scu} SCU, Class {ship_class}")
                            spaceships += 1
                    
                    formatted_data.append("All values are estimated")
                    
                    # Write in UEX format
                    with open('data/vehicles.txt', 'w', encoding='utf-8') as f:
                        f.write(" • ".join(formatted_data))
                    
                    print(f"Successfully cached {spaceships} spaceships")
                    return True
                else:
                    print(f"Failed to fetch vehicles: {response.status}")
                    return False
    except Exception as e:
        print(f"Error fetching vehicles: {e}")
        return False

async def test_fetch_locations():
    """Test fetching locations from UEX API"""
    location_endpoints = [
        f"{UEX_API_BASE}/cities",
        f"{UEX_API_BASE}/outposts", 
        f"{UEX_API_BASE}/space_stations",
        f"{UEX_API_BASE}/terminals"
    ]
    
    all_locations = []
    
    try:
        async with aiohttp.ClientSession() as session:
            for endpoint in location_endpoints:
                print(f"Fetching from: {endpoint}")
                try:
                    async with session.get(endpoint) as response:
                        if response.status == 200:
                            response_data = await response.json()
                            
                            # Handle the response format: {"status": "ok", "data": [...]}
                            if isinstance(response_data, dict) and 'data' in response_data:
                                locations = response_data['data']
                            else:
                                locations = response_data
                            
                            loc_type = get_location_type(endpoint)
                            for location in locations:
                                if isinstance(location, dict):
                                    name = location.get('name', 'Unknown')
                                    all_locations.append(f"{name} ({loc_type})")
                            print(f"  Found {len(locations)} {loc_type}s")
                        else:
                            print(f"  Failed to fetch from {endpoint}: {response.status}")
                except Exception as e:
                    print(f"  Error fetching from {endpoint}: {e}")
            
            # Convert to UEX format
            formatted_data = ["UEX", "Locations and Trading Posts", f"Updated {datetime.now().strftime('%b %d, %Y')}"]
            formatted_data.extend(all_locations)
            formatted_data.append("All values are estimated")
            
            # Write in UEX format
            with open('data/locations.txt', 'w', encoding='utf-8') as f:
                f.write(" • ".join(formatted_data))
            
            print(f"Successfully cached {len(all_locations)} locations")
            return True
    except Exception as e:
        print(f"Error fetching locations: {e}")
        return False

def get_ship_class(vehicle):
    """Determine ship class based on vehicle properties"""
    if vehicle.get('is_carrier', 0) == 1:
        return 5
    elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 100:
        return 4
    elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 50:
        return 3
    elif vehicle.get('is_cargo', 0) == 1:
        return 2
    else:
        return 1

def get_location_type(endpoint):
    """Determine location type based on endpoint"""
    if 'cities' in endpoint:
        return 'City'
    elif 'outposts' in endpoint:
        return 'Surface Outpost'
    elif 'space_stations' in endpoint:
        return 'Space Station'
    elif 'terminals' in endpoint:
        return 'Terminal'
    else:
        return 'Unknown'

async def main():
    print("Testing UEX API data fetching...")
    
    print("\n=== Testing Vehicles Fetch ===")
    vehicles_success = await test_fetch_vehicles()
    
    print("\n=== Testing Locations Fetch ===")
    locations_success = await test_fetch_locations()
    
    print(f"\n=== Results ===")
    print(f"Vehicles: {'✅ Success' if vehicles_success else '❌ Failed'}")
    print(f"Locations: {'✅ Success' if locations_success else '❌ Failed'}")
    
    if vehicles_success and locations_success:
        print("\nNow run 'python test_parsing.py' to verify the parsing works!")

if __name__ == "__main__":
    asyncio.run(main()) 