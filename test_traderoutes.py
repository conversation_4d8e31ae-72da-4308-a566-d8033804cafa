#!/usr/bin/env python3
"""
Test script for the new trade routes functionality
"""

import sys
import os
sys.path.append('.')

from cogs.traderoutes import TradeRoutes
import asyncio

class MockBot:
    pass

async def test_traderoutes():
    """Test the trade routes functionality"""
    print("Testing Trade Routes functionality...")
    
    # Create a mock bot and trade routes instance
    bot = MockBot()
    tr = TradeRoutes(bot)
    
    # Test parsing functions
    print("\n=== Testing Data Parsing ===")
    
    # Test commodities
    commodities = tr.get_commodities()
    print(f"Commodities loaded: {len(commodities)}")
    if commodities:
        print(f"First commodity: {commodities[0].get('name', 'Unknown')} (ID: {commodities[0].get('id', 'N/A')})")
    
    # Test routes
    routes = tr.get_routes()
    print(f"Routes loaded: {len(routes)}")
    if routes:
        route = routes[0]
        print(f"First route: Commodity ID {route.get('id_commodity')}, Profit: {route.get('profit', 0)} UEC")
    
    # Test vehicles
    vehicles = tr.get_vehicles()
    print(f"Vehicles loaded: {len(vehicles)}")
    if vehicles:
        vehicle = vehicles[0]
        print(f"First vehicle: {vehicle.get('name', 'Unknown')} - {vehicle.get('scu', 0)} SCU")
    
    # Test terminals
    terminals = tr.get_terminals()
    print(f"Terminals loaded: {len(terminals)}")
    if terminals:
        terminal = terminals[0]
        print(f"First terminal: {terminal.get('name', 'Unknown')} (ID: {terminal.get('id', 'N/A')})")
    
    # Test parsed data
    print("\n=== Testing Parsed Data ===")
    
    # Test commodity prices
    prices = tr.parse_commodities_prices()
    print(f"Parsed commodity prices: {len(prices)}")
    if prices:
        price = prices[0]
        print(f"First price: {price['commodity']} - Buy: {price['buy_price']}, Sell: {price['sell_price']}")
    
    # Test parsed routes
    parsed_routes = tr.parse_routes()
    print(f"Parsed routes: {len(parsed_routes)}")
    if parsed_routes:
        route = parsed_routes[0]
        print(f"First parsed route: {route['commodity']} from {route['buy_location']} to {route['sell_location']} - Profit: {route['profit']} UEC")
    
    # Test parsed vehicles
    parsed_vehicles = tr.parse_vehicles()
    print(f"Parsed vehicles: {len(parsed_vehicles)}")
    if parsed_vehicles:
        vehicle = parsed_vehicles[0]
        print(f"First parsed vehicle: {vehicle['name']} - {vehicle['scu']} SCU")
    
    # Test trade route calculation
    print("\n=== Testing Trade Route Calculation ===")
    if parsed_vehicles and parsed_routes:
        # Find a cargo ship
        cargo_ships = [v for v in parsed_vehicles if v['scu'] > 0]
        if cargo_ships:
            test_ship = cargo_ships[0]
            test_investment = 100000  # 100K UEC
            
            print(f"Testing with ship: {test_ship['name']} ({test_ship['scu']} SCU)")
            print(f"Investment: {test_investment} UEC")
            
            # Simulate the trade route finding logic
            best_routes = []
            for route in parsed_routes[:100]:  # Test first 100 routes
                price_origin = route.get('price_origin', 0)
                price_destination = route.get('price_destination', 0)
                
                if price_origin > 0 and price_destination > 0:
                    max_quantity = min(test_ship['scu'], test_investment // price_origin)
                    if max_quantity > 0:
                        total_cost = max_quantity * price_origin
                        total_revenue = max_quantity * price_destination
                        profit = total_revenue - total_cost
                        
                        if profit > 0:
                            best_routes.append({
                                'commodity': route['commodity'],
                                'profit': profit,
                                'roi': (profit / total_cost) * 100 if total_cost > 0 else 0
                            })
            
            best_routes.sort(key=lambda x: x['profit'], reverse=True)
            print(f"Found {len(best_routes)} profitable routes")
            
            if best_routes:
                for i, route in enumerate(best_routes[:5], 1):
                    print(f"  #{i}: {route['commodity']} - Profit: {route['profit']:.0f} UEC ({route['roi']:.1f}% ROI)")
            else:
                print("  No profitable routes found with current data")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(test_traderoutes())
