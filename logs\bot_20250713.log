2025-07-13 14:31:05,901 [INFO] Loaded cog: applytoaegis.py
2025-07-13 14:31:05,903 [INFO] Loaded cog: embedgen.py
2025-07-13 14:31:05,904 [INFO] Loaded cog: fleetdata.py
2025-07-13 14:31:05,906 [INFO] Loaded cog: giveaway.py
2025-07-13 14:31:05,909 [INFO] Loaded cog: moderation.py
2025-07-13 14:31:05,910 [INFO] Loaded cog: rolerequest.py
2025-07-13 14:31:05,911 [INFO] Loaded cog: rosters.py
2025-07-13 14:31:05,916 [INFO] Loaded cog: shipgame.py
2025-07-13 14:31:05,917 [INFO] Loaded cog: storagetracker.py
2025-07-13 14:31:05,919 [INFO] Loaded cog: voice.py
2025-07-13 14:31:05,922 [INFO] Loaded cog: voicetracker.py
2025-07-13 14:31:05,923 [INFO] Loaded cog: welcome.py
2025-07-13 14:31:05,923 [INFO] logging in using static token
2025-07-13 14:31:06,761 [INFO] Shard ID None has connected to Gateway (Session ID: 8533e5897987817a1f4350976b88fd18).
2025-07-13 14:31:08,782 [INFO] Logged in as Aegis Nox Bot
2025-07-13 14:31:09,004 [INFO] Synced 26 slash commands.
2025-07-13 16:37:47,060 [INFO] Shard ID None has successfully RESUMED session 8533e5897987817a1f4350976b88fd18.
2025-07-13 18:03:45,971 [INFO] Shard ID None has successfully RESUMED session 8533e5897987817a1f4350976b88fd18.
2025-07-13 19:27:01,397 [ERROR] Ignoring exception in view <ShipAttackView timeout=None children=4> for item <Button style=<ButtonStyle.danger: 4> url=None disabled=False label='Shoot 1' emoji=None row=None>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\shipgame.py", line 88, in shoot_callback
    await interaction.response.send_message(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 19:30:49,437 [INFO] Loaded cog: applytoaegis.py
2025-07-13 19:30:49,618 [ERROR] Failed to load cog commodityprices.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 244, in setup
    await bot.add_cog(CommodityPrices(bot))
                     ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 23, in __init__
    self.bot.loop.create_task(self.update_prices_loop())
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 140, in __getattr__
    raise AttributeError(msg)
AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.commodityprices' raised an error: AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

2025-07-13 19:30:49,620 [INFO] Loaded cog: embedgen.py
2025-07-13 19:30:49,621 [INFO] Loaded cog: fleetdata.py
2025-07-13 19:30:49,623 [INFO] Loaded cog: giveaway.py
2025-07-13 19:30:49,625 [INFO] Loaded cog: moderation.py
2025-07-13 19:30:49,627 [INFO] Loaded cog: rolerequest.py
2025-07-13 19:30:49,628 [INFO] Loaded cog: rosters.py
2025-07-13 19:30:49,630 [INFO] Loaded cog: shipgame.py
2025-07-13 19:30:49,631 [INFO] Loaded cog: storagetracker.py
2025-07-13 19:30:49,637 [INFO] Loaded cog: traderoutes.py
2025-07-13 19:30:49,638 [INFO] Loaded cog: voice.py
2025-07-13 19:30:49,640 [INFO] Loaded cog: voicetracker.py
2025-07-13 19:30:49,641 [INFO] Loaded cog: welcome.py
2025-07-13 19:30:49,641 [INFO] logging in using static token
2025-07-13 19:30:50,738 [INFO] Shard ID None has connected to Gateway (Session ID: 618a87dd0fb2632adcc70a2e087395d4).
2025-07-13 19:30:52,757 [INFO] Logged in as Aegis Nox Bot
2025-07-13 19:30:53,125 [INFO] Synced 30 slash commands.
2025-07-13 19:35:19,214 [INFO] Loaded cog: applytoaegis.py
2025-07-13 19:35:19,348 [ERROR] Failed to load cog commodityprices.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 246, in setup
    await bot.add_cog(CommodityPrices(bot))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 783, in add_cog
    cog = await cog._inject(self, override=override, guild=guild, guilds=guilds)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\cog.py", line 684, in _inject
    await maybe_coroutine(self.cog_load)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\utils.py", line 693, in maybe_coroutine
    return await value
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 243, in cog_load
    self.bot.loop.create_task(self.update_prices_loop())
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 140, in __getattr__
    raise AttributeError(msg)
AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.commodityprices' raised an error: AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

2025-07-13 19:35:19,350 [INFO] Loaded cog: embedgen.py
2025-07-13 19:35:19,351 [INFO] Loaded cog: fleetdata.py
2025-07-13 19:35:19,354 [INFO] Loaded cog: giveaway.py
2025-07-13 19:35:19,356 [INFO] Loaded cog: moderation.py
2025-07-13 19:35:19,358 [INFO] Loaded cog: rolerequest.py
2025-07-13 19:35:19,359 [INFO] Loaded cog: rosters.py
2025-07-13 19:35:19,360 [INFO] Loaded cog: shipgame.py
2025-07-13 19:35:19,361 [INFO] Loaded cog: storagetracker.py
2025-07-13 19:35:19,367 [INFO] Loaded cog: traderoutes.py
2025-07-13 19:35:19,369 [INFO] Loaded cog: voice.py
2025-07-13 19:35:19,384 [INFO] Loaded cog: voicetracker.py
2025-07-13 19:35:19,385 [INFO] Loaded cog: welcome.py
2025-07-13 19:35:19,385 [INFO] logging in using static token
2025-07-13 19:35:20,147 [INFO] Shard ID None has connected to Gateway (Session ID: 03ded6f4988456e72a9ead3a9f746ea5).
2025-07-13 19:35:22,159 [INFO] Logged in as Aegis Nox Bot
2025-07-13 19:35:22,446 [INFO] Synced 30 slash commands.
2025-07-13 19:36:31,655 [INFO] Loaded cog: applytoaegis.py
2025-07-13 19:36:31,757 [INFO] Loaded cog: commodityprices.py
2025-07-13 19:36:31,758 [INFO] Loaded cog: embedgen.py
2025-07-13 19:36:31,759 [INFO] Loaded cog: fleetdata.py
2025-07-13 19:36:31,761 [INFO] Loaded cog: giveaway.py
2025-07-13 19:36:31,764 [INFO] Loaded cog: moderation.py
2025-07-13 19:36:31,766 [INFO] Loaded cog: rolerequest.py
2025-07-13 19:36:31,767 [INFO] Loaded cog: rosters.py
2025-07-13 19:36:31,768 [INFO] Loaded cog: shipgame.py
2025-07-13 19:36:31,769 [INFO] Loaded cog: storagetracker.py
2025-07-13 19:36:31,770 [INFO] Loaded cog: traderoutes.py
2025-07-13 19:36:31,771 [INFO] Loaded cog: voice.py
2025-07-13 19:36:31,782 [INFO] Loaded cog: voicetracker.py
2025-07-13 19:36:31,783 [INFO] Loaded cog: welcome.py
2025-07-13 19:36:31,783 [INFO] logging in using static token
2025-07-13 19:36:32,554 [INFO] Shard ID None has connected to Gateway (Session ID: 8af4c89679ff2453095c0f34c91ea446).
2025-07-13 19:36:34,562 [INFO] Logged in as Aegis Nox Bot
2025-07-13 19:36:34,782 [INFO] Synced 34 slash commands.
2025-07-13 19:41:37,319 [INFO] Loaded cog: applytoaegis.py
2025-07-13 19:41:37,419 [INFO] Loaded cog: commodityprices.py
2025-07-13 19:41:37,420 [INFO] Loaded cog: embedgen.py
2025-07-13 19:41:37,422 [INFO] Loaded cog: fleetdata.py
2025-07-13 19:41:37,424 [INFO] Loaded cog: giveaway.py
2025-07-13 19:41:37,426 [INFO] Loaded cog: moderation.py
2025-07-13 19:41:37,427 [INFO] Loaded cog: rolerequest.py
2025-07-13 19:41:37,429 [INFO] Loaded cog: rosters.py
2025-07-13 19:41:37,430 [INFO] Loaded cog: shipgame.py
2025-07-13 19:41:37,431 [INFO] Loaded cog: storagetracker.py
2025-07-13 19:41:37,432 [INFO] Loaded cog: traderoutes.py
2025-07-13 19:41:37,433 [INFO] Loaded cog: voice.py
2025-07-13 19:41:37,435 [INFO] Loaded cog: voicetracker.py
2025-07-13 19:41:37,436 [INFO] Loaded cog: welcome.py
2025-07-13 19:41:37,436 [INFO] logging in using static token
2025-07-13 19:41:38,218 [INFO] Shard ID None has connected to Gateway (Session ID: 15d720a7c1fca33cba71369bfc0c057a).
2025-07-13 19:41:40,252 [INFO] Logged in as Aegis Nox Bot
2025-07-13 19:41:40,520 [INFO] Synced 34 slash commands.
2025-07-13 19:44:06,628 [INFO] Loaded cog: applytoaegis.py
2025-07-13 19:44:06,736 [INFO] Loaded cog: commodityprices.py
2025-07-13 19:44:06,737 [INFO] Loaded cog: embedgen.py
2025-07-13 19:44:06,739 [INFO] Loaded cog: fleetdata.py
2025-07-13 19:44:06,741 [INFO] Loaded cog: giveaway.py
2025-07-13 19:44:06,744 [INFO] Loaded cog: moderation.py
2025-07-13 19:44:06,746 [INFO] Loaded cog: rolerequest.py
2025-07-13 19:44:06,748 [INFO] Loaded cog: rosters.py
2025-07-13 19:44:06,749 [INFO] Loaded cog: shipgame.py
2025-07-13 19:44:06,750 [INFO] Loaded cog: storagetracker.py
2025-07-13 19:44:06,751 [INFO] Loaded cog: traderoutes.py
2025-07-13 19:44:06,752 [INFO] Loaded cog: voice.py
2025-07-13 19:44:06,763 [INFO] Loaded cog: voicetracker.py
2025-07-13 19:44:06,764 [INFO] Loaded cog: welcome.py
2025-07-13 19:44:06,764 [INFO] logging in using static token
2025-07-13 19:44:07,609 [INFO] Shard ID None has connected to Gateway (Session ID: 12a7815c4e7fd21e5a95659292e0d6df).
2025-07-13 19:44:09,604 [INFO] Logged in as Aegis Nox Bot
2025-07-13 19:44:09,869 [INFO] Synced 34 slash commands.
2025-07-13 19:59:48,207 [INFO] Loaded cog: applytoaegis.py
2025-07-13 19:59:48,213 [ERROR] Failed to load cog commodityprices.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 151, in setup
    await bot.add_cog(CommodityPrices(bot))
                     ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 21, in __init__
    self.bot.loop.create_task(self.update_commodities_loop())
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 140, in __getattr__
    raise AttributeError(msg)
AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.commodityprices' raised an error: AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

2025-07-13 19:59:48,215 [INFO] Loaded cog: embedgen.py
2025-07-13 19:59:48,216 [INFO] Loaded cog: fleetdata.py
2025-07-13 19:59:48,219 [INFO] Loaded cog: giveaway.py
2025-07-13 19:59:48,223 [INFO] Loaded cog: moderation.py
2025-07-13 19:59:48,223 [INFO] Loaded cog: rolerequest.py
2025-07-13 19:59:48,225 [INFO] Loaded cog: rosters.py
2025-07-13 19:59:48,226 [INFO] Loaded cog: shipgame.py
2025-07-13 19:59:48,228 [INFO] Loaded cog: storagetracker.py
2025-07-13 19:59:48,233 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 170, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 21, in __init__
    self.bot.loop.create_task(self.update_data_loop())
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 140, in __getattr__
    raise AttributeError(msg)
AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: loop attribute cannot be accessed in non-async contexts. Consider using either an asynchronous main function and passing it to asyncio.run or using asynchronous initialisation hooks such as Client.setup_hook

2025-07-13 19:59:48,235 [INFO] Loaded cog: voice.py
2025-07-13 19:59:48,238 [INFO] Loaded cog: voicetracker.py
2025-07-13 19:59:48,238 [INFO] Loaded cog: welcome.py
2025-07-13 19:59:48,238 [INFO] logging in using static token
2025-07-13 19:59:48,948 [INFO] Shard ID None has connected to Gateway (Session ID: 341b6df760637b300823e4db344e9543).
2025-07-13 19:59:50,968 [INFO] Logged in as Aegis Nox Bot
2025-07-13 19:59:51,190 [INFO] Synced 26 slash commands.
2025-07-13 20:00:30,475 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:00:30,480 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:00:30,482 [INFO] Loaded cog: embedgen.py
2025-07-13 20:00:30,483 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:00:30,486 [INFO] Loaded cog: giveaway.py
2025-07-13 20:00:30,489 [INFO] Loaded cog: moderation.py
2025-07-13 20:00:30,490 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:00:30,492 [INFO] Loaded cog: rosters.py
2025-07-13 20:00:30,493 [INFO] Loaded cog: shipgame.py
2025-07-13 20:00:30,494 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:00:30,498 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:00:30,499 [INFO] Loaded cog: voice.py
2025-07-13 20:00:30,510 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:00:30,511 [INFO] Loaded cog: welcome.py
2025-07-13 20:00:30,511 [INFO] logging in using static token
2025-07-13 20:00:31,596 [INFO] Shard ID None has connected to Gateway (Session ID: 0f2ed042d53674fb92c2c722430449a1).
2025-07-13 20:00:33,616 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:00:33,967 [INFO] Synced 33 slash commands.
2025-07-13 20:02:10,136 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:02:10,141 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:02:10,142 [INFO] Loaded cog: embedgen.py
2025-07-13 20:02:10,143 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:02:10,146 [INFO] Loaded cog: giveaway.py
2025-07-13 20:02:10,149 [INFO] Loaded cog: moderation.py
2025-07-13 20:02:10,150 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:02:10,152 [INFO] Loaded cog: rosters.py
2025-07-13 20:02:10,153 [INFO] Loaded cog: shipgame.py
2025-07-13 20:02:10,154 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:02:10,158 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:02:10,159 [INFO] Loaded cog: voice.py
2025-07-13 20:02:10,161 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:02:10,162 [INFO] Loaded cog: welcome.py
2025-07-13 20:02:10,162 [INFO] logging in using static token
2025-07-13 20:02:10,997 [INFO] Shard ID None has connected to Gateway (Session ID: 4b441b7f4914bddceef0d9aca68d7530).
2025-07-13 20:02:13,018 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:02:13,341 [INFO] Synced 33 slash commands.
2025-07-13 20:02:36,032 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:02:36,036 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:02:36,038 [INFO] Loaded cog: embedgen.py
2025-07-13 20:02:36,039 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:02:36,042 [INFO] Loaded cog: giveaway.py
2025-07-13 20:02:36,045 [INFO] Loaded cog: moderation.py
2025-07-13 20:02:36,047 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:02:36,048 [INFO] Loaded cog: rosters.py
2025-07-13 20:02:36,050 [INFO] Loaded cog: shipgame.py
2025-07-13 20:02:36,051 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:02:36,055 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:02:36,057 [INFO] Loaded cog: voice.py
2025-07-13 20:02:36,067 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:02:36,068 [INFO] Loaded cog: welcome.py
2025-07-13 20:02:36,069 [INFO] logging in using static token
2025-07-13 20:02:36,875 [INFO] Shard ID None has connected to Gateway (Session ID: c70d5144e4f7ddf4087d37a49d52b318).
2025-07-13 20:02:38,908 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:03:13,332 [INFO] Synced 33 slash commands.
2025-07-13 20:12:07,685 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:12:07,690 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:12:07,691 [INFO] Loaded cog: embedgen.py
2025-07-13 20:12:07,692 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:12:07,696 [INFO] Loaded cog: giveaway.py
2025-07-13 20:12:07,698 [INFO] Loaded cog: moderation.py
2025-07-13 20:12:07,699 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:12:07,701 [INFO] Loaded cog: rosters.py
2025-07-13 20:12:07,702 [INFO] Loaded cog: shipgame.py
2025-07-13 20:12:07,703 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:12:07,707 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:12:07,709 [INFO] Loaded cog: voice.py
2025-07-13 20:12:07,711 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:12:07,712 [INFO] Loaded cog: welcome.py
2025-07-13 20:12:07,712 [INFO] logging in using static token
2025-07-13 20:12:08,427 [INFO] Shard ID None has connected to Gateway (Session ID: 11112af6436decb61d781088f209ef32).
2025-07-13 20:12:10,441 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:12:10,675 [INFO] Synced 33 slash commands.
2025-07-13 20:15:04,079 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:15:04,084 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:15:04,085 [INFO] Loaded cog: embedgen.py
2025-07-13 20:15:04,087 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:15:04,090 [INFO] Loaded cog: giveaway.py
2025-07-13 20:15:04,092 [INFO] Loaded cog: moderation.py
2025-07-13 20:15:04,094 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:15:04,095 [INFO] Loaded cog: rosters.py
2025-07-13 20:15:04,096 [INFO] Loaded cog: shipgame.py
2025-07-13 20:15:04,098 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:15:04,103 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:15:04,104 [INFO] Loaded cog: voice.py
2025-07-13 20:15:04,114 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:15:04,115 [INFO] Loaded cog: welcome.py
2025-07-13 20:15:04,115 [INFO] logging in using static token
2025-07-13 20:15:05,065 [INFO] Shard ID None has connected to Gateway (Session ID: 8a6c267e24d5bde6fb2ef79d0fa07e8a).
2025-07-13 20:15:07,081 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:15:07,296 [INFO] Synced 33 slash commands.
2025-07-13 20:15:19,155 [ERROR] Ignoring exception in command 'ships'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 197, in list_ships
    await interaction.response.send_message(embed=embed)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 221, in request
    raise HTTPException(response, data)
discord.errors.HTTPException: 400 Bad Request (error code: 50035): Invalid Form Body
In data.embeds.0.fields: Must be 25 or fewer in length.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'ships' raised an exception: HTTPException: 400 Bad Request (error code: 50035): Invalid Form Body
In data.embeds.0.fields: Must be 25 or fewer in length.
2025-07-13 20:18:02,491 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:18:02,496 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:18:02,498 [INFO] Loaded cog: embedgen.py
2025-07-13 20:18:02,499 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:18:02,502 [INFO] Loaded cog: giveaway.py
2025-07-13 20:18:02,505 [INFO] Loaded cog: moderation.py
2025-07-13 20:18:02,506 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:18:02,508 [INFO] Loaded cog: rosters.py
2025-07-13 20:18:02,509 [INFO] Loaded cog: shipgame.py
2025-07-13 20:18:02,510 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:18:02,516 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:18:02,518 [INFO] Loaded cog: voice.py
2025-07-13 20:18:02,521 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:18:02,521 [INFO] Loaded cog: welcome.py
2025-07-13 20:18:02,521 [INFO] logging in using static token
2025-07-13 20:18:03,350 [INFO] Shard ID None has connected to Gateway (Session ID: b6c8e6efafddf9c49845be85ca07a8da).
2025-07-13 20:18:05,363 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:18:05,777 [INFO] Synced 33 slash commands.
2025-07-13 20:18:22,556 [ERROR] Ignoring exception in view <ShipPaginationView timeout=180.0 children=2> for item <ShipPageButton style=<ButtonStyle.primary: 1> url=None disabled=False label='Next' emoji=None row=None>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 269, in callback
    await interaction.client.get_cog("TradeRoutes").list_ships(interaction, page=new_page)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'Command' object is not callable
2025-07-13 20:20:02,299 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:20:02,301 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:20:02,302 [INFO] Loaded cog: embedgen.py
2025-07-13 20:20:02,303 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:20:02,306 [INFO] Loaded cog: giveaway.py
2025-07-13 20:20:02,308 [INFO] Loaded cog: moderation.py
2025-07-13 20:20:02,309 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:20:02,311 [INFO] Loaded cog: rosters.py
2025-07-13 20:20:02,312 [INFO] Loaded cog: shipgame.py
2025-07-13 20:20:02,313 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:20:02,319 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:20:02,320 [INFO] Loaded cog: voice.py
2025-07-13 20:20:02,331 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:20:02,332 [INFO] Loaded cog: welcome.py
2025-07-13 20:20:02,333 [INFO] logging in using static token
2025-07-13 20:20:03,122 [INFO] Shard ID None has connected to Gateway (Session ID: 2d9620804b93ff0d1eca5cd370fbd891).
2025-07-13 20:20:05,138 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:20:05,371 [INFO] Synced 33 slash commands.
2025-07-13 20:20:10,899 [ERROR] Ignoring exception in view <ShipPaginationView timeout=180 children=2> for item <ShipPageButton style=<ButtonStyle.primary: 1> url=None disabled=False label='Next' emoji=None row=None>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 283, in callback
    await interaction.response.edit_message(embed=embed, view=view)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 921, in edit_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 221, in request
    raise HTTPException(response, data)
discord.errors.HTTPException: 400 Bad Request (error code: 50035): Invalid Form Body
In data.embeds.0.fields.0.value: Must be 1024 or fewer in length.
2025-07-13 20:21:08,613 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:21:08,615 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:21:08,616 [INFO] Loaded cog: embedgen.py
2025-07-13 20:21:08,618 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:21:08,620 [INFO] Loaded cog: giveaway.py
2025-07-13 20:21:08,623 [INFO] Loaded cog: moderation.py
2025-07-13 20:21:08,624 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:21:08,626 [INFO] Loaded cog: rosters.py
2025-07-13 20:21:08,627 [INFO] Loaded cog: shipgame.py
2025-07-13 20:21:08,627 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:21:08,633 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:21:08,635 [INFO] Loaded cog: voice.py
2025-07-13 20:21:08,645 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:21:08,645 [INFO] Loaded cog: welcome.py
2025-07-13 20:21:08,645 [INFO] logging in using static token
2025-07-13 20:21:09,320 [INFO] Shard ID None has connected to Gateway (Session ID: 7cc40d457357437f1b0265f82ba8b997).
2025-07-13 20:21:11,333 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:21:11,574 [INFO] Synced 33 slash commands.
2025-07-13 20:21:18,741 [ERROR] Ignoring exception in view <ShipPaginationView timeout=180 children=2> for item <ShipPageButton style=<ButtonStyle.primary: 1> url=None disabled=False label='Next' emoji=None row=None>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 307, in callback
    await interaction.response.edit_message(embed=embed, view=view)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 921, in edit_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 221, in request
    raise HTTPException(response, data)
discord.errors.HTTPException: 400 Bad Request (error code: 50035): Invalid Form Body
In data.embeds.0.fields.0.value: Must be 1024 or fewer in length.
2025-07-13 20:22:18,816 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:22:18,818 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:22:18,820 [INFO] Loaded cog: embedgen.py
2025-07-13 20:22:18,821 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:22:18,824 [INFO] Loaded cog: giveaway.py
2025-07-13 20:22:18,826 [INFO] Loaded cog: moderation.py
2025-07-13 20:22:18,828 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:22:18,829 [INFO] Loaded cog: rosters.py
2025-07-13 20:22:18,830 [INFO] Loaded cog: shipgame.py
2025-07-13 20:22:18,831 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:22:18,837 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:22:18,838 [INFO] Loaded cog: voice.py
2025-07-13 20:22:18,841 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:22:18,841 [INFO] Loaded cog: welcome.py
2025-07-13 20:22:18,841 [INFO] logging in using static token
2025-07-13 20:22:19,616 [INFO] Shard ID None has connected to Gateway (Session ID: 67887bdf86f1b3932038afce80cb59f4).
2025-07-13 20:22:21,656 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:22:21,843 [INFO] Synced 33 slash commands.
2025-07-13 20:22:31,364 [ERROR] Ignoring exception in view <ShipPaginationView timeout=180 children=2> for item <ShipPageButton style=<ButtonStyle.primary: 1> url=None disabled=False label='Next' emoji=None row=None>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 313, in callback
    await interaction.response.edit_message(embed=embed, view=view)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 921, in edit_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 221, in request
    raise HTTPException(response, data)
discord.errors.HTTPException: 400 Bad Request (error code: 50035): Invalid Form Body
In data.embeds.0.fields.0.value: Must be 1024 or fewer in length.
2025-07-13 20:25:47,991 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:25:47,993 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:25:47,995 [INFO] Loaded cog: embedgen.py
2025-07-13 20:25:47,996 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:25:48,000 [INFO] Loaded cog: giveaway.py
2025-07-13 20:25:48,002 [INFO] Loaded cog: moderation.py
2025-07-13 20:25:48,003 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:25:48,005 [INFO] Loaded cog: rosters.py
2025-07-13 20:25:48,006 [INFO] Loaded cog: shipgame.py
2025-07-13 20:25:48,007 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:25:48,013 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 935, in _load_from_module_spec
    spec.loader.exec_module(lib)  # type: ignore
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 51, in <module>
    class TradeRoutes(commands.Cog):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 186, in TradeRoutes
    @calculate_route.autocomplete('commodity')
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 1076, in decorator
    raise TypeError(f'unknown parameter: {name!r}') from None
TypeError: unknown parameter: 'commodity'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 938, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: TypeError: unknown parameter: 'commodity'

2025-07-13 20:25:48,014 [INFO] Loaded cog: voice.py
2025-07-13 20:25:48,025 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:25:48,026 [INFO] Loaded cog: welcome.py
2025-07-13 20:25:48,026 [INFO] logging in using static token
2025-07-13 20:25:48,848 [INFO] Shard ID None has connected to Gateway (Session ID: 3d5bf9a50edbba2f70d05477e7a8d5d6).
2025-07-13 20:25:50,861 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:25:51,102 [INFO] Synced 29 slash commands.
2025-07-13 20:27:05,209 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:27:05,211 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:27:05,213 [INFO] Loaded cog: embedgen.py
2025-07-13 20:27:05,214 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:27:05,217 [INFO] Loaded cog: giveaway.py
2025-07-13 20:27:05,220 [INFO] Loaded cog: moderation.py
2025-07-13 20:27:05,221 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:27:05,223 [INFO] Loaded cog: rosters.py
2025-07-13 20:27:05,224 [INFO] Loaded cog: shipgame.py
2025-07-13 20:27:05,225 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:27:05,230 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:27:05,231 [INFO] Loaded cog: voice.py
2025-07-13 20:27:05,234 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:27:05,234 [INFO] Loaded cog: welcome.py
2025-07-13 20:27:05,235 [INFO] logging in using static token
2025-07-13 20:27:06,071 [INFO] Shard ID None has connected to Gateway (Session ID: 7a050349761e2ff7413e61c13a4618f7).
2025-07-13 20:27:08,086 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:27:08,285 [INFO] Synced 33 slash commands.
2025-07-13 20:28:19,958 [ERROR] Ignoring exception in command 'traderoute'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 164, in calculate_route
    routes = await self.get_optimal_routes(ship, investment, risk_level)
                   ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'get_optimal_routes'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoute' raised an exception: AttributeError: 'TradeRoutes' object has no attribute 'get_optimal_routes'
2025-07-13 20:29:11,854 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:29:11,856 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:29:11,857 [INFO] Loaded cog: embedgen.py
2025-07-13 20:29:11,858 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:29:11,861 [INFO] Loaded cog: giveaway.py
2025-07-13 20:29:11,864 [INFO] Loaded cog: moderation.py
2025-07-13 20:29:11,865 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:29:11,866 [INFO] Loaded cog: rosters.py
2025-07-13 20:29:11,867 [INFO] Loaded cog: shipgame.py
2025-07-13 20:29:11,868 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:29:11,873 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:29:11,875 [INFO] Loaded cog: voice.py
2025-07-13 20:29:11,886 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:29:11,887 [INFO] Loaded cog: welcome.py
2025-07-13 20:29:11,887 [INFO] logging in using static token
2025-07-13 20:29:12,696 [INFO] Shard ID None has connected to Gateway (Session ID: 29094c4b3afac713c4f3fa356ba0c97c).
2025-07-13 20:29:14,741 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:29:15,077 [INFO] Synced 33 slash commands.
2025-07-13 20:36:44,040 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:36:44,044 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:36:44,046 [INFO] Loaded cog: embedgen.py
2025-07-13 20:36:44,047 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:36:44,050 [INFO] Loaded cog: giveaway.py
2025-07-13 20:36:44,053 [INFO] Loaded cog: moderation.py
2025-07-13 20:36:44,054 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:36:44,056 [INFO] Loaded cog: rosters.py
2025-07-13 20:36:44,057 [INFO] Loaded cog: shipgame.py
2025-07-13 20:36:44,058 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:36:44,303 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 247, in setup
    await bot.add_cog(TradeRoutes(bot))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 783, in add_cog
    cog = await cog._inject(self, override=override, guild=guild, guilds=guilds)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\cog.py", line 684, in _inject
    await maybe_coroutine(self.cog_load)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\utils.py", line 693, in maybe_coroutine
    return await value
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 32, in cog_load
    await self.fetch_and_cache_uex_data_extract()
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 42, in fetch_and_cache_uex_data_extract
    data = await response.json()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\client_reqrep.py", line 1281, in json
    raise ContentTypeError(
aiohttp.client_exceptions.ContentTypeError: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain;charset=utf-8', url='https://api.uexcorp.space/2.0/data_extract'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: ContentTypeError: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain;charset=utf-8', url='https://api.uexcorp.space/2.0/data_extract'

2025-07-13 20:36:44,306 [INFO] Loaded cog: voice.py
2025-07-13 20:36:44,309 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:36:44,309 [INFO] Loaded cog: welcome.py
2025-07-13 20:36:44,310 [INFO] logging in using static token
2025-07-13 20:36:45,308 [INFO] Shard ID None has connected to Gateway (Session ID: 47c2fb222de2cf5483b7bf2612e412a6).
2025-07-13 20:36:47,332 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:36:47,553 [INFO] Synced 29 slash commands.
2025-07-13 20:37:56,704 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:37:56,706 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:37:56,708 [INFO] Loaded cog: embedgen.py
2025-07-13 20:37:56,709 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:37:56,712 [INFO] Loaded cog: giveaway.py
2025-07-13 20:37:56,715 [INFO] Loaded cog: moderation.py
2025-07-13 20:37:56,716 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:37:56,718 [INFO] Loaded cog: rosters.py
2025-07-13 20:37:56,719 [INFO] Loaded cog: shipgame.py
2025-07-13 20:37:56,720 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:37:56,914 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 247, in setup
    await bot.add_cog(TradeRoutes(bot))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 783, in add_cog
    cog = await cog._inject(self, override=override, guild=guild, guilds=guilds)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\cog.py", line 684, in _inject
    await maybe_coroutine(self.cog_load)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\utils.py", line 693, in maybe_coroutine
    return await value
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 32, in cog_load
    await self.fetch_and_cache_uex_data_extract()
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 42, in fetch_and_cache_uex_data_extract
    data = await response.json(content_type=None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\client_reqrep.py", line 1298, in json
    return loads(stripped.decode(encoding))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: JSONDecodeError: Expecting value: line 1 column 1 (char 0)

2025-07-13 20:37:56,916 [INFO] Loaded cog: voice.py
2025-07-13 20:37:56,926 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:37:56,927 [INFO] Loaded cog: welcome.py
2025-07-13 20:37:56,927 [INFO] logging in using static token
2025-07-13 20:37:56,928 [ERROR] Unhandled exception in internal background task 'update_data_extract'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 29, in update_data_extract
    await self.fetch_and_cache_uex_data_extract()
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 42, in fetch_and_cache_uex_data_extract
    data = await response.json(content_type=None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\client_reqrep.py", line 1298, in json
    return loads(stripped.decode(encoding))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
2025-07-13 20:37:57,643 [INFO] Shard ID None has connected to Gateway (Session ID: 23199e8526851baef0c9a7bfd08dfd97).
2025-07-13 20:37:59,655 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:37:59,937 [INFO] Synced 29 slash commands.
2025-07-13 20:38:36,388 [ERROR] Task exception was never retrieved
future: <Task finished name='Task-3' coro=<Loop._loop() done, defined at C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py:200> exception=JSONDecodeError('Expecting value: line 1 column 1 (char 0)')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 264, in _loop
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 29, in update_data_extract
    await self.fetch_and_cache_uex_data_extract()
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 42, in fetch_and_cache_uex_data_extract
    try:
         
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\client_reqrep.py", line 1298, in json
    return loads(stripped.decode(encoding))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
2025-07-13 20:38:37,870 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:38:37,872 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:38:37,873 [INFO] Loaded cog: embedgen.py
2025-07-13 20:38:37,875 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:38:37,877 [INFO] Loaded cog: giveaway.py
2025-07-13 20:38:37,881 [INFO] Loaded cog: moderation.py
2025-07-13 20:38:37,882 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:38:37,884 [INFO] Loaded cog: rosters.py
2025-07-13 20:38:37,885 [INFO] Loaded cog: shipgame.py
2025-07-13 20:38:37,886 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:38:38,077 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:38:38,079 [INFO] Loaded cog: voice.py
2025-07-13 20:38:38,089 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:38:38,090 [INFO] Loaded cog: welcome.py
2025-07-13 20:38:38,090 [INFO] logging in using static token
2025-07-13 20:38:38,930 [INFO] Shard ID None has connected to Gateway (Session ID: 80a61f4e93b0f0f63d8ed658a4cb6340).
2025-07-13 20:38:40,940 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:38:41,191 [INFO] Synced 32 slash commands.
2025-07-13 20:39:12,864 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:39:12,866 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:39:12,867 [INFO] Loaded cog: embedgen.py
2025-07-13 20:39:12,868 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:39:12,871 [INFO] Loaded cog: giveaway.py
2025-07-13 20:39:12,874 [INFO] Loaded cog: moderation.py
2025-07-13 20:39:12,875 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:39:12,876 [INFO] Loaded cog: rosters.py
2025-07-13 20:39:12,877 [INFO] Loaded cog: shipgame.py
2025-07-13 20:39:12,879 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:39:13,081 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:39:13,082 [INFO] Loaded cog: voice.py
2025-07-13 20:39:13,092 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:39:13,093 [INFO] Loaded cog: welcome.py
2025-07-13 20:39:13,093 [INFO] logging in using static token
2025-07-13 20:39:13,793 [INFO] Shard ID None has connected to Gateway (Session ID: 8c5f425a540e53b03d27ffd00e5ff311).
2025-07-13 20:39:15,815 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:39:16,079 [INFO] Synced 32 slash commands.
2025-07-13 20:39:47,565 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:39:47,569 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:39:47,572 [INFO] Loaded cog: embedgen.py
2025-07-13 20:39:47,574 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:39:47,578 [INFO] Loaded cog: giveaway.py
2025-07-13 20:39:47,583 [INFO] Loaded cog: moderation.py
2025-07-13 20:39:47,586 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:39:47,588 [INFO] Loaded cog: rosters.py
2025-07-13 20:39:47,590 [INFO] Loaded cog: shipgame.py
2025-07-13 20:39:47,591 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:39:47,784 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:39:47,786 [INFO] Loaded cog: voice.py
2025-07-13 20:39:47,797 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:39:47,797 [INFO] Loaded cog: welcome.py
2025-07-13 20:39:47,797 [INFO] logging in using static token
2025-07-13 20:39:48,516 [INFO] Shard ID None has connected to Gateway (Session ID: 26bd95c1db078649d9a7cdb88a947bc2).
2025-07-13 20:39:50,541 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:39:50,885 [INFO] Synced 32 slash commands.
2025-07-13 20:46:24,057 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:46:24,061 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:46:24,062 [INFO] Loaded cog: embedgen.py
2025-07-13 20:46:24,063 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:46:24,066 [INFO] Loaded cog: giveaway.py
2025-07-13 20:46:24,069 [INFO] Loaded cog: moderation.py
2025-07-13 20:46:24,070 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:46:24,072 [INFO] Loaded cog: rosters.py
2025-07-13 20:46:24,073 [INFO] Loaded cog: shipgame.py
2025-07-13 20:46:24,074 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:46:24,708 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:46:24,710 [INFO] Loaded cog: voice.py
2025-07-13 20:46:24,712 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:46:24,713 [INFO] Loaded cog: welcome.py
2025-07-13 20:46:24,713 [INFO] logging in using static token
2025-07-13 20:46:25,441 [INFO] Shard ID None has connected to Gateway (Session ID: fb4fd8db549e7b20b2550af70eaac697).
2025-07-13 20:46:27,456 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:46:27,793 [INFO] Synced 33 slash commands.
2025-07-13 20:54:48,814 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:54:48,819 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:54:48,820 [INFO] Loaded cog: embedgen.py
2025-07-13 20:54:48,822 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:54:48,824 [INFO] Loaded cog: giveaway.py
2025-07-13 20:54:48,827 [INFO] Loaded cog: moderation.py
2025-07-13 20:54:48,828 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:54:48,830 [INFO] Loaded cog: rosters.py
2025-07-13 20:54:48,831 [INFO] Loaded cog: shipgame.py
2025-07-13 20:54:48,832 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:54:49,404 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:54:49,406 [INFO] Loaded cog: voice.py
2025-07-13 20:54:49,408 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:54:49,409 [INFO] Loaded cog: welcome.py
2025-07-13 20:54:49,409 [INFO] logging in using static token
2025-07-13 20:54:50,215 [INFO] Shard ID None has connected to Gateway (Session ID: 29b5da69f7543e47ecf7f09a10915703).
2025-07-13 20:54:52,235 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:54:52,546 [INFO] Synced 35 slash commands.
2025-07-13 20:56:58,390 [INFO] Loaded cog: applytoaegis.py
2025-07-13 20:56:58,392 [INFO] Loaded cog: commodityprices.py
2025-07-13 20:56:58,393 [INFO] Loaded cog: embedgen.py
2025-07-13 20:56:58,395 [INFO] Loaded cog: fleetdata.py
2025-07-13 20:56:58,397 [INFO] Loaded cog: giveaway.py
2025-07-13 20:56:58,401 [INFO] Loaded cog: moderation.py
2025-07-13 20:56:58,403 [INFO] Loaded cog: rolerequest.py
2025-07-13 20:56:58,404 [INFO] Loaded cog: rosters.py
2025-07-13 20:56:58,406 [INFO] Loaded cog: shipgame.py
2025-07-13 20:56:58,407 [INFO] Loaded cog: storagetracker.py
2025-07-13 20:56:59,173 [INFO] Loaded cog: traderoutes.py
2025-07-13 20:56:59,175 [INFO] Loaded cog: voice.py
2025-07-13 20:56:59,177 [INFO] Loaded cog: voicetracker.py
2025-07-13 20:56:59,178 [INFO] Loaded cog: welcome.py
2025-07-13 20:56:59,178 [INFO] logging in using static token
2025-07-13 20:57:00,059 [INFO] Shard ID None has connected to Gateway (Session ID: 81cf2805127f2f229381ec6dc5e30211).
2025-07-13 20:57:02,071 [INFO] Logged in as Aegis Nox Bot
2025-07-13 20:57:02,298 [INFO] Synced 35 slash commands.
2025-07-13 20:57:32,590 [ERROR] Ignoring exception in command 'refreshdata'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 462, in refresh_data
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'refreshdata' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:07:01,442 [INFO] Loaded cog: applytoaegis.py
2025-07-13 21:07:01,443 [INFO] Loaded cog: commodityprices.py
2025-07-13 21:07:01,445 [INFO] Loaded cog: embedgen.py
2025-07-13 21:07:01,446 [INFO] Loaded cog: fleetdata.py
2025-07-13 21:07:01,448 [INFO] Loaded cog: giveaway.py
2025-07-13 21:07:01,452 [INFO] Loaded cog: moderation.py
2025-07-13 21:07:01,453 [INFO] Loaded cog: rolerequest.py
2025-07-13 21:07:01,454 [INFO] Loaded cog: rosters.py
2025-07-13 21:07:01,455 [INFO] Loaded cog: shipgame.py
2025-07-13 21:07:01,456 [INFO] Loaded cog: storagetracker.py
2025-07-13 21:07:02,460 [INFO] Loaded cog: traderoutes.py
2025-07-13 21:07:02,462 [INFO] Loaded cog: voice.py
2025-07-13 21:07:02,464 [INFO] Loaded cog: voicetracker.py
2025-07-13 21:07:02,465 [INFO] Loaded cog: welcome.py
2025-07-13 21:07:02,465 [INFO] logging in using static token
2025-07-13 21:07:03,345 [INFO] Shard ID None has connected to Gateway (Session ID: d20b0a9ef5a38f01b047c166f3f20b95).
2025-07-13 21:07:05,353 [INFO] Logged in as Aegis Nox Bot
2025-07-13 21:07:05,635 [INFO] Synced 35 slash commands.
2025-07-13 21:07:20,845 [ERROR] Ignoring exception in command 'ships'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 272, in list_ships
    commodity, rest = item.split(':', 1)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'ships' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:08:03,672 [ERROR] Ignoring exception in command 'traderoute'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 448, in find_trade_route
    @app_commands.command(name="locations", description="List all known locations.")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoute' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:08:51,051 [ERROR] Ignoring exception in command 'traderoute'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 559, in find_trade_route
    await interaction.response.send_message(embed=embed)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoute' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:09:31,400 [ERROR] Ignoring exception in command 'commodities'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 413, in list_commodities
    await interaction.response.send_message(embed=embed)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'commodities' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:10:24,515 [ERROR] Ignoring exception in command 'prices'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 115, in view_prices
    await interaction.response.send_message(embed=embed)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'prices' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:17:48,774 [INFO] Loaded cog: applytoaegis.py
2025-07-13 21:17:48,794 [ERROR] Failed to load cog commodityprices.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 935, in _load_from_module_spec
    spec.loader.exec_module(lib)  # type: ignore
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\commodityprices.py", line 116, in <module>
    @app_commands.command(name="prices", description="View current commodity prices")
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 1975, in decorator
    return Command(
           ^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 666, in __init__
    self._params: Dict[str, CommandParameter] = _extract_parameters_from_callback(callback, callback.__globals__)
                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 374, in _extract_parameters_from_callback
    param = annotation_to_parameter(resolved, parameter)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\transformers.py", line 834, in annotation_to_parameter
    (inner, default, validate_default) = get_supported_annotation(annotation)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\transformers.py", line 793, in get_supported_annotation
    raise TypeError(f'unsupported type annotation {annotation!r}')
TypeError: unsupported type annotation <class 'discord.interactions.Interaction'>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 938, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.commodityprices' raised an error: TypeError: unsupported type annotation <class 'discord.interactions.Interaction'>

2025-07-13 21:17:48,796 [INFO] Loaded cog: embedgen.py
2025-07-13 21:17:48,797 [INFO] Loaded cog: fleetdata.py
2025-07-13 21:17:48,800 [INFO] Loaded cog: giveaway.py
2025-07-13 21:17:48,803 [INFO] Loaded cog: moderation.py
2025-07-13 21:17:48,805 [INFO] Loaded cog: rolerequest.py
2025-07-13 21:17:48,807 [INFO] Loaded cog: rosters.py
2025-07-13 21:17:48,809 [INFO] Loaded cog: shipgame.py
2025-07-13 21:17:48,810 [INFO] Loaded cog: storagetracker.py
2025-07-13 21:17:49,832 [INFO] Loaded cog: traderoutes.py
2025-07-13 21:17:49,834 [INFO] Loaded cog: voice.py
2025-07-13 21:17:49,837 [ERROR] Failed to load cog voicetracker.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 530, in setup
    await bot.add_cog(VoiceTracker(bot))
                     ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 64, in __init__
    self.load_data()
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 71, in load_data
    self.voice_times = json.load(f)
                       ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 293, in load
    return loads(fp.read(),
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\decoder.py", line 340, in decode
    raise JSONDecodeError("Extra data", s, end)
json.decoder.JSONDecodeError: Extra data: line 337 column 2 (char 10217)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.voicetracker' raised an error: JSONDecodeError: Extra data: line 337 column 2 (char 10217)

2025-07-13 21:17:49,838 [INFO] Loaded cog: welcome.py
2025-07-13 21:17:49,839 [INFO] logging in using static token
2025-07-13 21:17:50,998 [INFO] Shard ID None has connected to Gateway (Session ID: 3fcfb471938ec59f3f94a6d157d5aa6d).
2025-07-13 21:17:53,002 [INFO] Logged in as Aegis Nox Bot
2025-07-13 21:17:53,447 [INFO] Synced 27 slash commands.
2025-07-13 21:17:59,586 [INFO] Shard ID None has successfully RESUMED session 29b5da69f7543e47ecf7f09a10915703.
2025-07-13 21:19:16,462 [INFO] Loaded cog: applytoaegis.py
2025-07-13 21:19:16,468 [INFO] Loaded cog: commodityprices.py
2025-07-13 21:19:16,469 [INFO] Loaded cog: embedgen.py
2025-07-13 21:19:16,470 [INFO] Loaded cog: fleetdata.py
2025-07-13 21:19:16,474 [INFO] Loaded cog: giveaway.py
2025-07-13 21:19:16,476 [INFO] Loaded cog: moderation.py
2025-07-13 21:19:16,477 [INFO] Loaded cog: rolerequest.py
2025-07-13 21:19:16,478 [INFO] Loaded cog: rosters.py
2025-07-13 21:19:16,479 [INFO] Loaded cog: shipgame.py
2025-07-13 21:19:16,480 [INFO] Loaded cog: storagetracker.py
2025-07-13 21:19:17,538 [INFO] Loaded cog: traderoutes.py
2025-07-13 21:19:17,540 [INFO] Loaded cog: voice.py
2025-07-13 21:19:17,542 [INFO] Loaded cog: voicetracker.py
2025-07-13 21:19:17,543 [INFO] Loaded cog: welcome.py
2025-07-13 21:19:17,543 [INFO] logging in using static token
2025-07-13 21:19:18,267 [INFO] Shard ID None has connected to Gateway (Session ID: 0695cfb3c1e0ce514afe3fc110af0d84).
2025-07-13 21:19:20,275 [INFO] Logged in as Aegis Nox Bot
2025-07-13 21:19:20,590 [INFO] Synced 35 slash commands.
2025-07-13 21:19:37,695 [ERROR] Ignoring exception in command 'traderoute'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 608, in find_trade_route
    await interaction.response.send_message(embed=embed)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoute' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:26:40,614 [INFO] Loaded cog: applytoaegis.py
2025-07-13 21:26:40,616 [INFO] Loaded cog: commodityprices.py
2025-07-13 21:26:40,617 [INFO] Loaded cog: embedgen.py
2025-07-13 21:26:40,618 [INFO] Loaded cog: fleetdata.py
2025-07-13 21:26:40,621 [INFO] Loaded cog: giveaway.py
2025-07-13 21:26:40,624 [INFO] Loaded cog: moderation.py
2025-07-13 21:26:40,625 [INFO] Loaded cog: rolerequest.py
2025-07-13 21:26:40,626 [INFO] Loaded cog: rosters.py
2025-07-13 21:26:40,627 [INFO] Loaded cog: shipgame.py
2025-07-13 21:26:40,628 [INFO] Loaded cog: storagetracker.py
2025-07-13 21:26:41,778 [INFO] Loaded cog: traderoutes.py
2025-07-13 21:26:41,780 [INFO] Loaded cog: voice.py
2025-07-13 21:26:41,782 [INFO] Loaded cog: voicetracker.py
2025-07-13 21:26:41,783 [INFO] Loaded cog: welcome.py
2025-07-13 21:26:41,783 [INFO] logging in using static token
2025-07-13 21:26:43,025 [INFO] Shard ID None has connected to Gateway (Session ID: 1ac61df711c1b7ac937a11bcb11cbfbc).
2025-07-13 21:26:45,034 [INFO] Logged in as Aegis Nox Bot
2025-07-13 21:26:45,281 [INFO] Synced 36 slash commands.
2025-07-13 21:27:01,109 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 21:28:51,734 [INFO] Loaded cog: applytoaegis.py
2025-07-13 21:28:51,736 [INFO] Loaded cog: commodityprices.py
2025-07-13 21:28:51,737 [INFO] Loaded cog: embedgen.py
2025-07-13 21:28:51,739 [INFO] Loaded cog: fleetdata.py
2025-07-13 21:28:51,742 [INFO] Loaded cog: giveaway.py
2025-07-13 21:28:51,744 [INFO] Loaded cog: moderation.py
2025-07-13 21:28:51,746 [INFO] Loaded cog: rolerequest.py
2025-07-13 21:28:51,748 [INFO] Loaded cog: rosters.py
2025-07-13 21:28:51,749 [INFO] Loaded cog: shipgame.py
2025-07-13 21:28:51,750 [INFO] Loaded cog: storagetracker.py
2025-07-13 21:28:52,708 [INFO] Loaded cog: traderoutes.py
2025-07-13 21:28:52,709 [INFO] Loaded cog: voice.py
2025-07-13 21:28:52,712 [INFO] Loaded cog: voicetracker.py
2025-07-13 21:28:52,713 [INFO] Loaded cog: welcome.py
2025-07-13 21:28:52,713 [INFO] logging in using static token
2025-07-13 21:28:53,602 [INFO] Shard ID None has connected to Gateway (Session ID: 88903c98295c239ddfb70b31ad8c3cbd).
2025-07-13 21:28:55,635 [INFO] Logged in as Aegis Nox Bot
2025-07-13 21:28:55,818 [INFO] Synced 36 slash commands.
2025-07-13 21:28:57,908 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 21:30:50,108 [INFO] Loaded cog: applytoaegis.py
2025-07-13 21:30:50,110 [INFO] Loaded cog: commodityprices.py
2025-07-13 21:30:50,111 [INFO] Loaded cog: embedgen.py
2025-07-13 21:30:50,112 [INFO] Loaded cog: fleetdata.py
2025-07-13 21:30:50,116 [INFO] Loaded cog: giveaway.py
2025-07-13 21:30:50,119 [INFO] Loaded cog: moderation.py
2025-07-13 21:30:50,120 [INFO] Loaded cog: rolerequest.py
2025-07-13 21:30:50,121 [INFO] Loaded cog: rosters.py
2025-07-13 21:30:50,123 [INFO] Loaded cog: shipgame.py
2025-07-13 21:30:50,124 [INFO] Loaded cog: storagetracker.py
2025-07-13 21:30:51,060 [INFO] Loaded cog: traderoutes.py
2025-07-13 21:30:51,062 [INFO] Loaded cog: voice.py
2025-07-13 21:30:51,064 [INFO] Loaded cog: voicetracker.py
2025-07-13 21:30:51,065 [INFO] Loaded cog: welcome.py
2025-07-13 21:30:51,065 [INFO] logging in using static token
2025-07-13 21:30:51,965 [INFO] Shard ID None has connected to Gateway (Session ID: 5c112d8a479eae4510f96dca3bf6ee39).
2025-07-13 21:30:53,973 [INFO] Logged in as Aegis Nox Bot
2025-07-13 21:30:54,229 [INFO] Synced 36 slash commands.
2025-07-13 21:30:57,186 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 21:31:10,060 [ERROR] Ignoring exception in command 'traderoute'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 378, in find_trade_route
    return []
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoute' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 21:47:00,638 [INFO] Shard ID None has successfully RESUMED session 5c112d8a479eae4510f96dca3bf6ee39.
2025-07-13 22:37:21,726 [INFO] Shard ID None has successfully RESUMED session 5c112d8a479eae4510f96dca3bf6ee39.
2025-07-13 22:46:38,078 [INFO] Loaded cog: applytoaegis.py
2025-07-13 22:46:38,080 [INFO] Loaded cog: commodityprices.py
2025-07-13 22:46:38,081 [INFO] Loaded cog: embedgen.py
2025-07-13 22:46:38,083 [INFO] Loaded cog: fleetdata.py
2025-07-13 22:46:38,087 [INFO] Loaded cog: giveaway.py
2025-07-13 22:46:38,089 [INFO] Loaded cog: moderation.py
2025-07-13 22:46:38,092 [INFO] Loaded cog: rolerequest.py
2025-07-13 22:46:38,093 [INFO] Loaded cog: rosters.py
2025-07-13 22:46:38,095 [INFO] Loaded cog: shipgame.py
2025-07-13 22:46:38,096 [INFO] Loaded cog: storagetracker.py
2025-07-13 22:46:39,019 [INFO] Loaded cog: traderoutes.py
2025-07-13 22:46:39,021 [INFO] Loaded cog: voice.py
2025-07-13 22:46:39,023 [INFO] Loaded cog: voicetracker.py
2025-07-13 22:46:39,024 [INFO] Loaded cog: welcome.py
2025-07-13 22:46:39,024 [INFO] logging in using static token
2025-07-13 22:46:39,784 [INFO] Shard ID None has connected to Gateway (Session ID: 0588dda56be1c911d214b275d5182e4b).
2025-07-13 22:46:41,795 [INFO] Logged in as Aegis Nox Bot
2025-07-13 22:46:42,012 [INFO] Synced 36 slash commands.
2025-07-13 22:46:47,630 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 22:50:33,493 [INFO] Loaded cog: applytoaegis.py
2025-07-13 22:50:33,495 [INFO] Loaded cog: commodityprices.py
2025-07-13 22:50:33,496 [INFO] Loaded cog: embedgen.py
2025-07-13 22:50:33,497 [INFO] Loaded cog: fleetdata.py
2025-07-13 22:50:33,500 [INFO] Loaded cog: giveaway.py
2025-07-13 22:50:33,504 [INFO] Loaded cog: moderation.py
2025-07-13 22:50:33,505 [INFO] Loaded cog: rolerequest.py
2025-07-13 22:50:33,507 [INFO] Loaded cog: rosters.py
2025-07-13 22:50:33,508 [INFO] Loaded cog: shipgame.py
2025-07-13 22:50:33,509 [INFO] Loaded cog: storagetracker.py
2025-07-13 22:50:34,418 [INFO] Loaded cog: traderoutes.py
2025-07-13 22:50:34,420 [INFO] Loaded cog: voice.py
2025-07-13 22:50:34,423 [INFO] Loaded cog: voicetracker.py
2025-07-13 22:50:34,424 [INFO] Loaded cog: welcome.py
2025-07-13 22:50:34,424 [INFO] logging in using static token
2025-07-13 22:50:35,184 [INFO] Shard ID None has connected to Gateway (Session ID: d631d0a926af15eba317f2123f539c83).
2025-07-13 22:50:37,212 [INFO] Logged in as Aegis Nox Bot
2025-07-13 22:50:37,384 [INFO] Synced 36 slash commands.
2025-07-13 22:50:39,913 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 22:50:47,928 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 22:52:53,536 [INFO] Loaded cog: applytoaegis.py
2025-07-13 22:52:53,537 [INFO] Loaded cog: commodityprices.py
2025-07-13 22:52:53,538 [INFO] Loaded cog: embedgen.py
2025-07-13 22:52:53,540 [INFO] Loaded cog: fleetdata.py
2025-07-13 22:52:53,543 [INFO] Loaded cog: giveaway.py
2025-07-13 22:52:53,546 [INFO] Loaded cog: moderation.py
2025-07-13 22:52:53,547 [INFO] Loaded cog: rolerequest.py
2025-07-13 22:52:53,549 [INFO] Loaded cog: rosters.py
2025-07-13 22:52:53,551 [INFO] Loaded cog: shipgame.py
2025-07-13 22:52:53,552 [INFO] Loaded cog: storagetracker.py
2025-07-13 22:52:54,494 [INFO] Loaded cog: traderoutes.py
2025-07-13 22:52:54,496 [INFO] Loaded cog: voice.py
2025-07-13 22:52:54,497 [INFO] Loaded cog: voicetracker.py
2025-07-13 22:52:54,498 [INFO] Loaded cog: welcome.py
2025-07-13 22:52:54,498 [INFO] logging in using static token
2025-07-13 22:52:55,203 [INFO] Shard ID None has connected to Gateway (Session ID: 3c49f96a2fc3e95fd9484f1130d97c7a).
2025-07-13 22:52:57,226 [INFO] Logged in as Aegis Nox Bot
2025-07-13 22:52:57,432 [INFO] Synced 36 slash commands.
2025-07-13 22:53:05,230 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 22:54:06,513 [INFO] Loaded cog: applytoaegis.py
2025-07-13 22:54:06,515 [INFO] Loaded cog: commodityprices.py
2025-07-13 22:54:06,517 [INFO] Loaded cog: embedgen.py
2025-07-13 22:54:06,518 [INFO] Loaded cog: fleetdata.py
2025-07-13 22:54:06,521 [INFO] Loaded cog: giveaway.py
2025-07-13 22:54:06,524 [INFO] Loaded cog: moderation.py
2025-07-13 22:54:06,526 [INFO] Loaded cog: rolerequest.py
2025-07-13 22:54:06,528 [INFO] Loaded cog: rosters.py
2025-07-13 22:54:06,529 [INFO] Loaded cog: shipgame.py
2025-07-13 22:54:06,530 [INFO] Loaded cog: storagetracker.py
2025-07-13 22:54:07,400 [INFO] Loaded cog: traderoutes.py
2025-07-13 22:54:07,402 [INFO] Loaded cog: voice.py
2025-07-13 22:54:07,404 [INFO] Loaded cog: voicetracker.py
2025-07-13 22:54:07,405 [INFO] Loaded cog: welcome.py
2025-07-13 22:54:07,405 [INFO] logging in using static token
2025-07-13 22:54:08,158 [INFO] Shard ID None has connected to Gateway (Session ID: 9dcd36827e725eabb7d2a8a5fe509108).
2025-07-13 22:54:10,170 [INFO] Logged in as Aegis Nox Bot
2025-07-13 22:54:10,479 [INFO] Synced 36 slash commands.
2025-07-13 22:54:26,819 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 22:56:21,829 [INFO] Loaded cog: applytoaegis.py
2025-07-13 22:56:21,831 [INFO] Loaded cog: commodityprices.py
2025-07-13 22:56:21,832 [INFO] Loaded cog: embedgen.py
2025-07-13 22:56:21,834 [INFO] Loaded cog: fleetdata.py
2025-07-13 22:56:21,837 [INFO] Loaded cog: giveaway.py
2025-07-13 22:56:21,840 [INFO] Loaded cog: moderation.py
2025-07-13 22:56:21,841 [INFO] Loaded cog: rolerequest.py
2025-07-13 22:56:21,843 [INFO] Loaded cog: rosters.py
2025-07-13 22:56:21,844 [INFO] Loaded cog: shipgame.py
2025-07-13 22:56:21,845 [INFO] Loaded cog: storagetracker.py
2025-07-13 22:56:22,747 [INFO] Loaded cog: traderoutes.py
2025-07-13 22:56:22,749 [INFO] Loaded cog: voice.py
2025-07-13 22:56:22,753 [INFO] Loaded cog: voicetracker.py
2025-07-13 22:56:22,755 [INFO] Loaded cog: welcome.py
2025-07-13 22:56:22,757 [INFO] logging in using static token
2025-07-13 22:56:23,413 [INFO] Shard ID None has connected to Gateway (Session ID: 73d3f7fdccd52672ab70ce5be5e99be5).
2025-07-13 22:56:25,444 [INFO] Logged in as Aegis Nox Bot
2025-07-13 22:56:25,721 [INFO] Synced 36 slash commands.
2025-07-13 22:56:29,903 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 22:57:20,378 [ERROR] Ignoring exception in command 'traderoute'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 448, in find_trade_route
    value=f"Buy: {self.format_price(p['buy_price'])}\nSell: {self.format_price(p['sell_price'])}",
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoute' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 23:00:25,187 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:00:25,189 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:00:25,190 [INFO] Loaded cog: embedgen.py
2025-07-13 23:00:25,191 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:00:25,195 [INFO] Loaded cog: giveaway.py
2025-07-13 23:00:25,198 [INFO] Loaded cog: moderation.py
2025-07-13 23:00:25,199 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:00:25,201 [INFO] Loaded cog: rosters.py
2025-07-13 23:00:25,202 [INFO] Loaded cog: shipgame.py
2025-07-13 23:00:25,203 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:00:26,237 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:00:26,239 [INFO] Loaded cog: voice.py
2025-07-13 23:00:26,241 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:00:26,242 [INFO] Loaded cog: welcome.py
2025-07-13 23:00:26,242 [INFO] logging in using static token
2025-07-13 23:00:28,889 [INFO] Shard ID None has connected to Gateway (Session ID: 72e540c597377c9a483a300fcd24700f).
2025-07-13 23:00:30,908 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:00:31,364 [INFO] Synced 36 slash commands.
2025-07-13 23:00:32,334 [ERROR] Ignoring exception in command tree
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1089, in wrapper
    await self._call(interaction)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1221, in _call
    command, options = self._get_app_command_options(data)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1125, in _get_app_command_options
    raise CommandNotFound(name, parents)
discord.app_commands.errors.CommandNotFound: Application command 'update_uex_data' not found
2025-07-13 23:09:46,373 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:09:46,375 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:09:46,376 [INFO] Loaded cog: embedgen.py
2025-07-13 23:09:46,378 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:09:46,381 [INFO] Loaded cog: giveaway.py
2025-07-13 23:09:46,384 [INFO] Loaded cog: moderation.py
2025-07-13 23:09:46,385 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:09:46,386 [INFO] Loaded cog: rosters.py
2025-07-13 23:09:46,388 [INFO] Loaded cog: shipgame.py
2025-07-13 23:09:46,388 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:09:46,960 [ERROR] Unhandled exception in internal background task 'update_data_extract'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 80, in update_data_extract
    self.last_update = datetime.now(datetime.timezone.utc).isoformat()
                                    ^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timezone'
2025-07-13 23:09:47,061 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 668, in setup
    await bot.add_cog(TradeRoutes(bot))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 783, in add_cog
    cog = await cog._inject(self, override=override, guild=guild, guilds=guilds)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\cog.py", line 684, in _inject
    await maybe_coroutine(self.cog_load)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\utils.py", line 693, in maybe_coroutine
    return await value
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 84, in cog_load
    await self.update_data_extract()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 368, in __call__
    return await self.coro(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 80, in update_data_extract
    self.last_update = datetime.now(datetime.timezone.utc).isoformat()
                                    ^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timezone'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: type object 'datetime.datetime' has no attribute 'timezone'

2025-07-13 23:09:47,063 [INFO] Loaded cog: voice.py
2025-07-13 23:09:47,065 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:09:47,066 [INFO] Loaded cog: welcome.py
2025-07-13 23:09:47,066 [INFO] logging in using static token
2025-07-13 23:09:47,850 [INFO] Shard ID None has connected to Gateway (Session ID: 163ec5d7b998d27998229e703d94b7f2).
2025-07-13 23:09:49,866 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:09:50,168 [INFO] Synced 29 slash commands.
2025-07-13 23:11:46,211 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:11:46,213 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:11:46,214 [INFO] Loaded cog: embedgen.py
2025-07-13 23:11:46,215 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:11:46,218 [INFO] Loaded cog: giveaway.py
2025-07-13 23:11:46,221 [INFO] Loaded cog: moderation.py
2025-07-13 23:11:46,223 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:11:46,224 [INFO] Loaded cog: rosters.py
2025-07-13 23:11:46,226 [INFO] Loaded cog: shipgame.py
2025-07-13 23:11:46,227 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:12:00,127 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:12:00,129 [INFO] Loaded cog: voice.py
2025-07-13 23:12:00,131 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:12:00,131 [INFO] Loaded cog: welcome.py
2025-07-13 23:12:00,131 [INFO] logging in using static token
2025-07-13 23:12:01,183 [INFO] Shard ID None has connected to Gateway (Session ID: ed6126d4a120d74365d7f0cb1b3c1eee).
2025-07-13 23:12:03,184 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:12:03,596 [INFO] Synced 35 slash commands.
2025-07-13 23:13:04,156 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:13:04,158 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:13:04,159 [INFO] Loaded cog: embedgen.py
2025-07-13 23:13:04,160 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:13:04,164 [INFO] Loaded cog: giveaway.py
2025-07-13 23:13:04,166 [INFO] Loaded cog: moderation.py
2025-07-13 23:13:04,167 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:13:04,169 [INFO] Loaded cog: rosters.py
2025-07-13 23:13:04,170 [INFO] Loaded cog: shipgame.py
2025-07-13 23:13:04,171 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:13:17,650 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:13:17,651 [INFO] Loaded cog: voice.py
2025-07-13 23:13:17,654 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:13:17,654 [INFO] Loaded cog: welcome.py
2025-07-13 23:13:17,654 [INFO] logging in using static token
2025-07-13 23:13:18,602 [INFO] Shard ID None has connected to Gateway (Session ID: 21490285af65859c8338c59e18ce0365).
2025-07-13 23:13:20,618 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:13:20,809 [INFO] Synced 35 slash commands.
2025-07-13 23:14:02,055 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:14:02,057 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:14:02,058 [INFO] Loaded cog: embedgen.py
2025-07-13 23:14:02,059 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:14:02,062 [INFO] Loaded cog: giveaway.py
2025-07-13 23:14:02,065 [INFO] Loaded cog: moderation.py
2025-07-13 23:14:02,066 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:14:02,067 [INFO] Loaded cog: rosters.py
2025-07-13 23:14:02,068 [INFO] Loaded cog: shipgame.py
2025-07-13 23:14:02,069 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:14:15,771 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:14:15,773 [INFO] Loaded cog: voice.py
2025-07-13 23:14:15,775 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:14:15,776 [INFO] Loaded cog: welcome.py
2025-07-13 23:14:15,776 [INFO] logging in using static token
2025-07-13 23:14:18,126 [INFO] Shard ID None has connected to Gateway (Session ID: 5b1935186d5c14d6e4df833371b891be).
2025-07-13 23:14:20,168 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:14:20,390 [INFO] Synced 35 slash commands.
2025-07-13 23:16:22,885 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:16:22,887 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:16:22,889 [INFO] Loaded cog: embedgen.py
2025-07-13 23:16:22,890 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:16:22,894 [INFO] Loaded cog: giveaway.py
2025-07-13 23:16:22,896 [INFO] Loaded cog: moderation.py
2025-07-13 23:16:22,898 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:16:22,899 [INFO] Loaded cog: rosters.py
2025-07-13 23:16:22,901 [INFO] Loaded cog: shipgame.py
2025-07-13 23:16:22,902 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:16:36,787 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:16:36,794 [INFO] Loaded cog: voice.py
2025-07-13 23:16:36,796 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:16:36,796 [INFO] Loaded cog: welcome.py
2025-07-13 23:16:36,797 [INFO] logging in using static token
2025-07-13 23:16:37,653 [INFO] Shard ID None has connected to Gateway (Session ID: db174ef89d9ed936e3d66d8097c27f76).
2025-07-13 23:16:39,658 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:16:39,989 [INFO] Synced 35 slash commands.
2025-07-13 23:17:02,429 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:17:02,430 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:17:02,432 [INFO] Loaded cog: embedgen.py
2025-07-13 23:17:02,433 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:17:02,435 [INFO] Loaded cog: giveaway.py
2025-07-13 23:17:02,440 [INFO] Loaded cog: moderation.py
2025-07-13 23:17:02,441 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:17:02,442 [INFO] Loaded cog: rosters.py
2025-07-13 23:17:02,443 [INFO] Loaded cog: shipgame.py
2025-07-13 23:17:02,444 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:17:17,266 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:17:17,268 [INFO] Loaded cog: voice.py
2025-07-13 23:17:17,270 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:17:17,272 [INFO] Loaded cog: welcome.py
2025-07-13 23:17:17,273 [INFO] logging in using static token
2025-07-13 23:17:17,917 [INFO] Shard ID None has connected to Gateway (Session ID: f7f0ecb59e99dddccb6157c3f840f9e9).
2025-07-13 23:17:19,926 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:17:20,165 [INFO] Synced 35 slash commands.
2025-07-13 23:17:38,453 [ERROR] Ignoring exception in command 'traderoute'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 448, in find_trade_route
    end = start + per_page
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoute' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 23:23:33,744 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:23:33,746 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:23:33,747 [INFO] Loaded cog: embedgen.py
2025-07-13 23:23:33,748 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:23:33,752 [INFO] Loaded cog: giveaway.py
2025-07-13 23:23:33,755 [INFO] Loaded cog: moderation.py
2025-07-13 23:23:33,756 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:23:33,757 [INFO] Loaded cog: rosters.py
2025-07-13 23:23:33,758 [INFO] Loaded cog: shipgame.py
2025-07-13 23:23:33,759 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:23:47,236 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:23:47,238 [INFO] Loaded cog: voice.py
2025-07-13 23:23:47,240 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:23:47,241 [INFO] Loaded cog: welcome.py
2025-07-13 23:23:47,241 [INFO] logging in using static token
2025-07-13 23:23:48,152 [INFO] Shard ID None has connected to Gateway (Session ID: 2f0cae044b6fd9bab6ddedc1067753ef).
2025-07-13 23:23:50,163 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:23:50,412 [INFO] Synced 35 slash commands.
2025-07-13 23:27:35,892 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:27:35,893 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:27:35,895 [INFO] Loaded cog: embedgen.py
2025-07-13 23:27:35,897 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:27:35,900 [INFO] Loaded cog: giveaway.py
2025-07-13 23:27:35,903 [INFO] Loaded cog: moderation.py
2025-07-13 23:27:35,904 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:27:35,906 [INFO] Loaded cog: rosters.py
2025-07-13 23:27:35,908 [INFO] Loaded cog: shipgame.py
2025-07-13 23:27:35,908 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:27:50,132 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:27:50,134 [INFO] Loaded cog: voice.py
2025-07-13 23:27:50,136 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:27:50,136 [INFO] Loaded cog: welcome.py
2025-07-13 23:27:50,137 [INFO] logging in using static token
2025-07-13 23:27:50,874 [INFO] Shard ID None has connected to Gateway (Session ID: f70b887770fc136faaf9e45a6513ab72).
2025-07-13 23:27:52,895 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:27:53,144 [INFO] Synced 35 slash commands.
2025-07-13 23:39:31,681 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:39:31,683 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:39:31,684 [INFO] Loaded cog: embedgen.py
2025-07-13 23:39:31,685 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:39:31,689 [INFO] Loaded cog: giveaway.py
2025-07-13 23:39:31,691 [INFO] Loaded cog: moderation.py
2025-07-13 23:39:31,693 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:39:31,695 [INFO] Loaded cog: rosters.py
2025-07-13 23:39:31,696 [INFO] Loaded cog: shipgame.py
2025-07-13 23:39:31,697 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:39:47,104 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:39:47,106 [INFO] Loaded cog: voice.py
2025-07-13 23:39:47,108 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:39:47,109 [INFO] Loaded cog: welcome.py
2025-07-13 23:39:47,109 [INFO] logging in using static token
2025-07-13 23:39:47,802 [INFO] Shard ID None has connected to Gateway (Session ID: 0df00a984c0f2663b5669db4527ce634).
2025-07-13 23:39:49,824 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:39:50,136 [INFO] Synced 35 slash commands.
2025-07-13 23:40:55,167 [INFO] Loaded cog: applytoaegis.py
2025-07-13 23:40:55,169 [INFO] Loaded cog: commodityprices.py
2025-07-13 23:40:55,170 [INFO] Loaded cog: embedgen.py
2025-07-13 23:40:55,171 [INFO] Loaded cog: fleetdata.py
2025-07-13 23:40:55,175 [INFO] Loaded cog: giveaway.py
2025-07-13 23:40:55,177 [INFO] Loaded cog: moderation.py
2025-07-13 23:40:55,178 [INFO] Loaded cog: rolerequest.py
2025-07-13 23:40:55,180 [INFO] Loaded cog: rosters.py
2025-07-13 23:40:55,181 [INFO] Loaded cog: shipgame.py
2025-07-13 23:40:55,182 [INFO] Loaded cog: storagetracker.py
2025-07-13 23:41:11,423 [INFO] Loaded cog: traderoutes.py
2025-07-13 23:41:11,425 [INFO] Loaded cog: voice.py
2025-07-13 23:41:11,427 [INFO] Loaded cog: voicetracker.py
2025-07-13 23:41:11,427 [INFO] Loaded cog: welcome.py
2025-07-13 23:41:11,427 [INFO] logging in using static token
2025-07-13 23:41:12,170 [INFO] Shard ID None has connected to Gateway (Session ID: 15e5ddccc06f5dfe5888d6fca7f4fb18).
2025-07-13 23:41:14,193 [INFO] Logged in as Aegis Nox Bot
2025-07-13 23:41:14,688 [INFO] Synced 35 slash commands.
2025-07-13 23:41:21,601 [ERROR] Ignoring exception in command 'refreshdata'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 462, in refresh_data
    inline=False
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'refreshdata' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
