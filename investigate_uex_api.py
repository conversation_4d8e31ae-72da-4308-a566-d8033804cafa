#!/usr/bin/env python3
"""
Investigate how the UEX website actually works
"""

import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

UEX_API_BASE = "https://api.uexcorp.space/2.0"
UEX_API_TOKEN = os.getenv('UEX_API_TOKEN')

async def investigate_api():
    """Investigate different API approaches"""
    headers = {}
    if UEX_API_TOKEN:
        headers['Authorization'] = f'Bearer {UEX_API_TOKEN}'
    
    print("Investigating UEX API approaches...")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Try to get routes with just investment parameter
        print("\n1. Testing routes with just investment parameter:")
        try:
            url = f"{UEX_API_BASE}/commodities_routes?investment=2000000"
            async with session.get(url, headers=headers) as response:
                print(f"   Status: {response.status}")
                if response.status != 200:
                    text = await response.text()
                    print(f"   Response: {text}")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 2: Get all commodities and try a few
        print("\n2. Getting commodities to understand the data:")
        try:
            url = f"{UEX_API_BASE}/commodities"
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, dict) and 'data' in data:
                        commodities = data['data']
                        print(f"   Found {len(commodities)} commodities")
                        
                        # Look for ETAM, BIOP, FROG from the screenshot
                        target_codes = ['ETAM', 'BIOP', 'FROG']
                        found_commodities = []
                        
                        for commodity in commodities:
                            code = commodity.get('code', '').upper()
                            name = commodity.get('name', '')
                            if code in target_codes:
                                found_commodities.append(commodity)
                                print(f"   Found: {code} = {name} (ID: {commodity.get('id')})")
                        
                        # Test routes for these specific commodities
                        print(f"\n3. Testing routes for commodities from screenshot:")
                        for commodity in found_commodities:
                            commodity_id = commodity.get('id')
                            code = commodity.get('code')
                            
                            url = f"{UEX_API_BASE}/commodities_routes?id_commodity={commodity_id}&investment=2000000"
                            try:
                                async with session.get(url, headers=headers) as response:
                                    if response.status == 200:
                                        route_data = await response.json()
                                        if isinstance(route_data, dict) and 'data' in route_data:
                                            routes = route_data['data']
                                            print(f"   {code}: {len(routes)} routes")
                                            
                                            if routes:
                                                # Show top route
                                                top_route = max(routes, key=lambda x: x.get('profit', 0))
                                                origin = top_route.get('origin_terminal_name', 'Unknown')
                                                dest = top_route.get('destination_terminal_name', 'Unknown')
                                                profit = top_route.get('profit', 0)
                                                investment = top_route.get('investment', 0)
                                                print(f"     Top: {origin} → {dest}, Profit: {profit:,.0f}, Investment: {investment:,.0f}")
                            except Exception as e:
                                print(f"   Error for {code}: {e}")
                                
        except Exception as e:
            print(f"   Error getting commodities: {e}")
        
        # Test 3: Try different approaches to get comprehensive routes
        print(f"\n4. Testing comprehensive route fetching:")
        
        # Try getting routes for multiple star systems
        try:
            url = f"{UEX_API_BASE}/star_systems"
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, dict) and 'data' in data:
                        systems = data['data']
                        print(f"   Found {len(systems)} star systems")
                        
                        # Try getting routes from Stanton system (most common)
                        stanton = next((s for s in systems if 'stanton' in s.get('name', '').lower()), None)
                        if stanton:
                            system_id = stanton.get('id')
                            print(f"   Testing routes from Stanton system (ID: {system_id})")
                            
                            url = f"{UEX_API_BASE}/commodities_routes?id_star_system_origin={system_id}&investment=2000000"
                            try:
                                async with session.get(url, headers=headers) as response:
                                    print(f"   Status: {response.status}")
                                    if response.status == 200:
                                        route_data = await response.json()
                                        if isinstance(route_data, dict) and 'data' in route_data:
                                            routes = route_data['data']
                                            print(f"   Found {len(routes)} routes from Stanton")
                                    else:
                                        text = await response.text()
                                        print(f"   Response: {text}")
                            except Exception as e:
                                print(f"   Error: {e}")
                                
        except Exception as e:
            print(f"   Error getting star systems: {e}")

if __name__ == "__main__":
    asyncio.run(investigate_api())
