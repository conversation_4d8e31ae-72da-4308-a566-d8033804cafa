#!/usr/bin/env python3
"""
Test script to verify the new investment-based route fetching
"""

import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

UEX_API_BASE = "https://api.uexcorp.space/2.0"
UEX_API_TOKEN = os.getenv('UEX_API_TOKEN')

async def test_investment_routes():
    """Test fetching routes with investment parameter"""
    investment = 2000000  # 2M UEC like in the screenshot
    # Try with a commodity ID first to see if it works
    url = f"{UEX_API_BASE}/commodities_routes?id_commodity=1&investment={investment}"
    
    headers = {}
    if UEX_API_TOKEN:
        headers['Authorization'] = f'Bearer {UEX_API_TOKEN}'
    
    print(f"Testing investment-based routes with {investment:,} UEC")
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                print(f"Response status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, dict) and 'data' in data:
                        routes = data['data']
                        print(f"Found {len(routes)} routes")
                        
                        # Show top 5 routes by profit
                        sorted_routes = sorted(routes, key=lambda x: x.get('profit', 0), reverse=True)
                        
                        print("\nTop 5 routes by profit:")
                        for i, route in enumerate(sorted_routes[:5], 1):
                            commodity_name = route.get('commodity_name', 'Unknown')
                            origin = route.get('origin_terminal_name', 'Unknown')
                            destination = route.get('destination_terminal_name', 'Unknown')
                            profit = route.get('profit', 0)
                            investment_needed = route.get('investment', 0)
                            price_origin = route.get('price_origin', 0)
                            price_destination = route.get('price_destination', 0)
                            distance = route.get('distance', 0)
                            
                            print(f"\n#{i} {commodity_name}")
                            print(f"  Buy: {origin}")
                            print(f"  Sell: {destination}")
                            print(f"  Investment: {investment_needed:,.0f} UEC")
                            print(f"  Profit: {profit:,.0f} UEC")
                            print(f"  Buy Price: {price_origin:.2f} UEC/SCU")
                            print(f"  Sell Price: {price_destination:.2f} UEC/SCU")
                            if distance > 0:
                                print(f"  Distance: {distance/1000000:.0f}km")
                    else:
                        print("Unexpected response format")
                        print(data)
                elif response.status == 401:
                    print("Authentication failed - check UEX_API_TOKEN")
                else:
                    print(f"API request failed with status {response.status}")
                    text = await response.text()
                    print(f"Response: {text}")
                    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_investment_routes())
