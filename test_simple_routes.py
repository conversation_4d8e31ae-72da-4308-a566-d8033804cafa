#!/usr/bin/env python3
"""
Simple test to understand UEX API routes endpoint
"""

import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

UEX_API_BASE = "https://api.uexcorp.space/2.0"
UEX_API_TOKEN = os.getenv('UEX_API_TOKEN')

async def test_simple():
    """Test basic routes endpoint"""
    headers = {}
    if UEX_API_TOKEN:
        headers['Authorization'] = f'Bearer {UEX_API_TOKEN}'
    
    # Test 1: Get routes for a specific commodity
    print("Test 1: Routes for commodity ID 1")
    url1 = f"{UEX_API_BASE}/commodities_routes?id_commodity=1"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url1, headers=headers) as response:
                print(f"Status: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, dict) and 'data' in data:
                        routes = data['data']
                        print(f"Found {len(routes)} routes")
                        if routes:
                            route = routes[0]
                            print(f"Sample route: {route.get('commodity_name')} from {route.get('origin_terminal_name')} to {route.get('destination_terminal_name')}")
                            print(f"Profit: {route.get('profit', 0):,.0f} UEC")
                else:
                    text = await response.text()
                    print(f"Error: {text}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "-"*50 + "\n")

    # Test 2: Try to get all routes by fetching multiple commodities
    print("Test 2: Get routes for multiple commodities and filter by investment")

    # First get list of commodities
    commodities_url = f"{UEX_API_BASE}/commodities"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(commodities_url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if isinstance(data, dict) and 'data' in data:
                        commodities = data['data'][:5]  # Test with first 5 commodities
                        print(f"Testing with {len(commodities)} commodities")

                        all_routes = []
                        for commodity in commodities:
                            commodity_id = commodity.get('id')
                            routes_url = f"{UEX_API_BASE}/commodities_routes?id_commodity={commodity_id}&investment=2000000"

                            async with session.get(routes_url, headers=headers) as routes_response:
                                if routes_response.status == 200:
                                    routes_data = await routes_response.json()
                                    if isinstance(routes_data, dict) and 'data' in routes_data:
                                        routes = routes_data['data']
                                        # Filter routes that fit within our investment
                                        for route in routes:
                                            if route.get('investment', 0) <= 2000000:
                                                all_routes.append(route)

                        # Sort by profit
                        all_routes.sort(key=lambda x: x.get('profit', 0), reverse=True)

                        print(f"Found {len(all_routes)} total routes within 2M investment")
                        print("\nTop 3 routes:")
                        for i, route in enumerate(all_routes[:3], 1):
                            print(f"{i}. {route.get('commodity_name')} from {route.get('origin_terminal_name')} to {route.get('destination_terminal_name')}")
                            print(f"   Profit: {route.get('profit', 0):,.0f} UEC, Investment: {route.get('investment', 0):,.0f} UEC")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_simple())
