import discord
from discord.ext import commands, tasks
from discord import app_commands
import os
import aiohttp
import asyncio
import json
from datetime import datetime, timedelta, timezone
from discord.ui import View, Button
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CACHE_TTL = timedelta(hours=6)
DATA_DIR = 'data'
COMMODITIES_FILE = os.path.join(DATA_DIR, 'commodities_cache.json')
ROUTES_FILE = os.path.join(DATA_DIR, 'routes_cache.json')
PRICES_FILE = os.path.join(DATA_DIR, 'prices_cache.json')
VEHICLES_FILE = os.path.join(DATA_DIR, 'vehicles_cache.json')
TERMINALS_FILE = os.path.join(DATA_DIR, 'terminals_cache.json')

UEX_API_BASE = "https://api.uexcorp.space/2.0"
UEX_API_TOKEN = os.getenv('UEX_API_TOKEN')

class PaginatedView(View):
    def __init__(self, get_embed, total_pages, initial_page=1, timeout=180):
        super().__init__(timeout=timeout)
        self.get_embed = get_embed
        self.total_pages = total_pages
        self.current_page = initial_page
        self.add_item(Button(label="◀", custom_id="prev", style=discord.ButtonStyle.primary, disabled=initial_page == 1))
        self.add_item(Button(label="▶", custom_id="next", style=discord.ButtonStyle.primary, disabled=initial_page == total_pages))

    async def update_buttons(self, interaction: discord.Interaction):
        prev_button = self.children[0]
        next_button = self.children[1]
        prev_button.disabled = self.current_page == 1
        next_button.disabled = self.current_page == self.total_pages
        embed = self.get_embed(self.current_page)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_button_click(self, interaction: discord.Interaction, button: Button):
        if button.custom_id == "prev" and self.current_page > 1:
            self.current_page -= 1
        elif button.custom_id == "next" and self.current_page < self.total_pages:
            self.current_page += 1
        await self.update_buttons(interaction)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # Allow only the user who invoked the command to interact
        return True

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True

@commands.Cog.listener()
async def on_interaction(interaction: discord.Interaction):
    if interaction.type.name == "component":
        view = interaction.message.components[0] if interaction.message.components else None
        if isinstance(view, PaginatedView):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)

class TradeRoutes(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.last_update = None
        # Ensure data directory exists
        os.makedirs(DATA_DIR, exist_ok=True)
        self.update_data_extract.start()

    def cog_unload(self):
        self.update_data_extract.cancel()

    @tasks.loop(hours=6)
    async def update_data_extract(self):
        await self.fetch_and_cache_commodities()
        await self.fetch_and_cache_routes()
        await self.fetch_and_cache_prices()
        await self.fetch_and_cache_vehicles()
        await self.fetch_and_cache_terminals()
        self.last_update = datetime.now(timezone.utc).isoformat()
        print(f"Updated UEX data at {self.last_update}")

    async def cog_load(self):
        await self.update_data_extract()

    async def fetch_and_cache_commodities(self):
        """Fetch commodities from UEX API"""
        url = f"{UEX_API_BASE}/commodities"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, dict) and 'data' in data:
                            commodities = data['data']
                            cache_data = {
                                'commodities': commodities,
                                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
                            }
                            with open(COMMODITIES_FILE, 'w', encoding='utf-8') as f:
                                json.dump(cache_data, f, indent=2)
                            print(f"Successfully cached {len(commodities)} commodities")
                    else:
                        print(f"Failed to fetch commodities: {response.status}")
        except Exception as e:
            print(f"Error fetching commodities: {e}")

    async def fetch_and_cache_routes(self):
        """Fetch all trade routes from UEX API"""
        try:
            # First get commodities to know which ones to fetch routes for
            commodities = self.get_commodities()
            if not commodities:
                print("No commodities available for route fetching")
                return

            all_routes = []
            async with aiohttp.ClientSession() as session:
                # Limit to first 10 commodities for testing to avoid rate limits
                limited_commodities = commodities[:10] if len(commodities) > 10 else commodities
                for commodity in limited_commodities:
                    commodity_id = commodity.get('id')
                    if commodity_id:
                        url = f"{UEX_API_BASE}/commodities_routes?id_commodity={commodity_id}"
                        try:
                            async with session.get(url) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    if isinstance(data, dict) and 'data' in data:
                                        routes = data['data']
                                        all_routes.extend(routes)
                        except Exception as e:
                            print(f"Error fetching routes for commodity {commodity_id}: {e}")

            cache_data = {
                'routes': all_routes,
                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
            }
            with open(ROUTES_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2)
            print(f"Successfully cached {len(all_routes)} routes")
        except Exception as e:
            print(f"Error fetching routes: {e}")

    async def fetch_and_cache_prices(self):
        """Fetch all commodity prices from UEX API"""
        try:
            # First get commodities to know which ones to fetch prices for
            commodities = self.get_commodities()
            if not commodities:
                print("No commodities available for price fetching")
                return

            all_prices = []
            async with aiohttp.ClientSession() as session:
                for commodity in commodities:
                    commodity_id = commodity.get('id')
                    if commodity_id:
                        url = f"{UEX_API_BASE}/commodities_prices?id_commodity={commodity_id}"
                        try:
                            async with session.get(url) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    if isinstance(data, dict) and 'data' in data:
                                        prices = data['data']
                                        all_prices.extend(prices)
                        except Exception as e:
                            print(f"Error fetching prices for commodity {commodity_id}: {e}")

            cache_data = {
                'prices': all_prices,
                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
            }
            with open(PRICES_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2)
            print(f"Successfully cached {len(all_prices)} prices")
        except Exception as e:
            print(f"Error fetching prices: {e}")

    async def fetch_and_cache_terminals(self):
        """Fetch terminals from UEX API"""
        url = f"{UEX_API_BASE}/terminals"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, dict) and 'data' in data:
                            terminals = data['data']
                            cache_data = {
                                'terminals': terminals,
                                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
                            }
                            with open(TERMINALS_FILE, 'w', encoding='utf-8') as f:
                                json.dump(cache_data, f, indent=2)
                            print(f"Successfully cached {len(terminals)} terminals")
                    else:
                        print(f"Failed to fetch terminals: {response.status}")
        except Exception as e:
            print(f"Error fetching terminals: {e}")

    async def fetch_and_cache_vehicles(self):
        """Fetch vehicles from the JSON API endpoint"""
        url = f"{UEX_API_BASE}/vehicles"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        response_data = await response.json()

                        # Handle the response format: {"status": "ok", "data": [...]}
                        if isinstance(response_data, dict) and 'data' in response_data:
                            vehicles_data = response_data['data']
                        else:
                            vehicles_data = response_data

                        # Filter for spaceships only and cache as JSON
                        spaceships = []
                        for vehicle in vehicles_data:
                            if isinstance(vehicle, dict) and vehicle.get('is_spaceship', 0) == 1:
                                spaceships.append({
                                    'name': vehicle.get('name', 'Unknown'),
                                    'scu': vehicle.get('scu', 0),
                                    'class': self.get_ship_class(vehicle),
                                    'is_cargo': vehicle.get('is_cargo', 0) == 1
                                })

                        cache_data = {
                            'vehicles': spaceships,
                            'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
                        }
                        with open(VEHICLES_FILE, 'w', encoding='utf-8') as f:
                            json.dump(cache_data, f, indent=2)

                        print(f"Successfully cached {len(spaceships)} spaceships")
                    else:
                        print(f"Failed to fetch vehicles: {response.status}")
        except Exception as e:
            print(f"Error fetching vehicles: {e}")

    def get_commodities(self):
        """Get cached commodities data"""
        try:
            if os.path.exists(COMMODITIES_FILE):
                with open(COMMODITIES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('commodities', [])
        except Exception as e:
            print(f"Error reading commodities cache: {e}")
        return []

    def get_routes(self):
        """Get cached routes data"""
        try:
            if os.path.exists(ROUTES_FILE):
                with open(ROUTES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('routes', [])
        except Exception as e:
            print(f"Error reading routes cache: {e}")
        return []

    def get_prices(self):
        """Get cached prices data"""
        try:
            if os.path.exists(PRICES_FILE):
                with open(PRICES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('prices', [])
        except Exception as e:
            print(f"Error reading prices cache: {e}")
        return []

    def get_vehicles(self):
        """Get cached vehicles data"""
        try:
            if os.path.exists(VEHICLES_FILE):
                with open(VEHICLES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('vehicles', [])
        except Exception as e:
            print(f"Error reading vehicles cache: {e}")
        return []

    def get_terminals(self):
        """Get cached terminals data"""
        try:
            if os.path.exists(TERMINALS_FILE):
                with open(TERMINALS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('terminals', [])
        except Exception as e:
            print(f"Error reading terminals cache: {e}")
        return []

    def parse_commodities_prices(self):
        """Parse cached commodity prices from JSON"""
        commodities = self.get_commodities()
        if not commodities:
            return []

        prices = []
        for commodity in commodities:
            if isinstance(commodity, dict):
                buy_price = commodity.get('price_buy', 0)
                sell_price = commodity.get('price_sell', 0)

                if buy_price > 0 or sell_price > 0:
                    prices.append({
                        'commodity': commodity.get('name', 'Unknown'),
                        'buy_price': buy_price,
                        'sell_price': sell_price,
                        'avg_price': (buy_price + sell_price) // 2 if buy_price > 0 and sell_price > 0 else max(buy_price, sell_price)
                    })

        return prices

    def parse_routes(self):
        """Parse cached trade routes from JSON"""
        routes = self.get_routes()
        terminals = self.get_terminals()
        commodities = self.get_commodities()

        if not routes:
            return []

        # Create lookup dictionaries
        terminal_lookup = {t.get('id'): t.get('name', f"Terminal {t.get('id')}") for t in terminals if isinstance(t, dict)}
        commodity_lookup = {c.get('id'): c.get('name', f"Commodity {c.get('id')}") for c in commodities if isinstance(c, dict)}

        parsed_routes = []
        for route in routes:
            if isinstance(route, dict):
                commodity_id = route.get('id_commodity')
                origin_id = route.get('id_terminal_origin')
                dest_id = route.get('id_terminal_destination')
                profit = route.get('profit', 0)
                price_origin = route.get('price_origin', 0)
                price_destination = route.get('price_destination', 0)

                if commodity_id and origin_id and dest_id and profit > 0:
                    parsed_routes.append({
                        'commodity': commodity_lookup.get(commodity_id, f"ID{commodity_id}"),
                        'buy_location': terminal_lookup.get(origin_id, f"Terminal {origin_id}"),
                        'sell_location': terminal_lookup.get(dest_id, f"Terminal {dest_id}"),
                        'profit': profit,
                        'price_origin': price_origin,
                        'price_destination': price_destination,
                        'distance': route.get('distance', 0),
                        'investment': route.get('investment', 0)
                    })

        return parsed_routes

    def parse_vehicles(self):
        """Parse cached vehicles from JSON"""
        return self.get_vehicles()

    def parse_locations(self):
        """Parse cached terminals as locations"""
        terminals = self.get_terminals()
        locations = []

        for terminal in terminals:
            if isinstance(terminal, dict):
                locations.append({
                    'name': terminal.get('name', 'Unknown'),
                    'type': 'Terminal'
                })

        return locations

    def get_ship_class(self, vehicle):
        """Determine ship class based on vehicle properties"""
        if vehicle.get('is_carrier', 0) == 1:
            return 5
        elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 100:
            return 4
        elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 50:
            return 3
        elif vehicle.get('is_cargo', 0) == 1:
            return 2
        else:
            return 1



    def format_price(self, price):
        if price is None or price == 0:
            return "N/A"
        if price >= 1_000_000:
            return f"{price // 1_000_000}M UEC"
        elif price >= 1_000:
            return f"{price // 1_000}K UEC"
        else:
            return f"{price} UEC"

    @app_commands.command(name="ships", description="List available ships and their cargo capacities.")
    async def list_ships(self, interaction: discord.Interaction, page: int = 1):
        ships = self.parse_vehicles()
        if not ships:
            await interaction.response.send_message("No ship data available. Please check if the data file is valid.", ephemeral=True)
            return
        cargo_ships = [s for s in ships if s['scu'] > 0]
        if not cargo_ships:
            await interaction.response.send_message("No cargo ships found in the data.", ephemeral=True)
            return
        per_page = 25
        total_pages = (len(cargo_ships) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Available Cargo Ships (Page {page}/{total_pages})",
                color=discord.Color.blue()
            )
            for ship in cargo_ships[start:end]:
                embed.add_field(
                    name=ship['name'],
                    value=f"SCU: {ship['scu']} | Class: {ship['class']}",
                    inline=True
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="commodities", description="List available commodities and their prices.")
    async def list_commodities(self, interaction: discord.Interaction, page: int = 1):
        prices = self.parse_commodities_prices()
        if not prices:
            await interaction.response.send_message("No commodity price data available. Please check if the data file is valid.", ephemeral=True)
            return
        per_page = 10
        total_pages = (len(prices) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Commodities (Page {page}/{total_pages})",
                color=discord.Color.green()
            )
            for p in prices[start:end]:
                embed.add_field(
                    name=p['commodity'],
                    value=f"Buy: {self.format_price(p['buy_price'])}\nSell: {self.format_price(p['sell_price'])}",
                    inline=False
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="routes", description="List top trade routes.")
    async def list_routes(self, interaction: discord.Interaction, page: int = 1):
        routes = self.parse_routes()
        if not routes:
            await interaction.response.send_message("No trade route data available. Please check if the data file is valid.", ephemeral=True)
            return
        sorted_routes = sorted(routes, key=lambda x: x['profit'], reverse=True)
        per_page = 10
        total_pages = (len(sorted_routes) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Top Trade Routes (Page {page}/{total_pages})",
                color=discord.Color.gold()
            )
            for r in sorted_routes[start:end]:
                embed.add_field(
                    name=f"{r['commodity']}",
                    value=f"Buy: {r['buy_location']}\nSell: {r['sell_location']}\nProfit: {self.format_price(r['profit'])}",
                    inline=False
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="locations", description="List all known locations.")
    async def list_locations(self, interaction: discord.Interaction, page: int = 1):
        locations = self.parse_locations()
        if not locations:
            await interaction.response.send_message("No location data available. Please check if the data file is valid.", ephemeral=True)
            return
        
        per_page = 25
        total_pages = (len(locations) + per_page - 1) // per_page
        page = max(1, min(page, total_pages))
        start = (page - 1) * per_page
        end = start + per_page
        
        embed = discord.Embed(
            title=f"Locations (Page {page}/{total_pages})",
            color=discord.Color.purple()
        )
        
        for loc in locations[start:end]:
            embed.add_field(
                name=loc['name'], 
                value=f"Type: {loc['type']}", 
                inline=True
            )
        
        if self.last_update:
            embed.set_footer(text=f"Last updated: {self.last_update}")
        
        await interaction.response.send_message(embed=embed)

    async def fetch_routes_for_investment(self, investment: int, max_cargo: int):
        """Fetch routes from UEX API for specific investment amount and cargo capacity"""
        headers = {}
        if UEX_API_TOKEN:
            headers['Authorization'] = f'Bearer {UEX_API_TOKEN}'

        try:
            # Get list of commodities
            commodities = self.get_commodities()
            if not commodities:
                print("No commodities available for route fetching")
                return []

            all_routes = []
            async with aiohttp.ClientSession() as session:
                # Process all commodities to get comprehensive route data
                # Limit to avoid rate limits but include more commodities for better results
                limited_commodities = commodities[:50] if len(commodities) > 50 else commodities

                print(f"Fetching routes for {len(limited_commodities)} commodities...")

                for i, commodity in enumerate(limited_commodities):
                    commodity_id = commodity.get('id')
                    commodity_name = commodity.get('name', 'Unknown')
                    commodity_code = commodity.get('code', '')

                    if commodity_id:
                        # Add investment parameter to the API call
                        url = f"{UEX_API_BASE}/commodities_routes?id_commodity={commodity_id}&investment={investment}"
                        try:
                            async with session.get(url, headers=headers) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    if isinstance(data, dict) and 'data' in data:
                                        routes = data['data']

                                        # Filter and process routes
                                        for route in routes:
                                            if isinstance(route, dict):
                                                route_investment = route.get('investment', 0)
                                                price_origin = route.get('price_origin', 0)
                                                profit = route.get('profit', 0)

                                                # Check if route fits within investment and cargo
                                                if (route_investment <= investment and
                                                    route_investment > 0 and
                                                    price_origin > 0 and
                                                    profit > 0):

                                                    scu_needed = route_investment / price_origin
                                                    if scu_needed <= max_cargo:
                                                        # Add commodity info to route for easier processing
                                                        route['commodity_name'] = commodity_name
                                                        route['commodity_code'] = commodity_code
                                                        route['scu_needed'] = scu_needed
                                                        all_routes.append(route)

                                elif response.status == 401:
                                    print("API authentication failed. Check UEX_API_TOKEN in .env file")
                                    return []
                                elif response.status == 429:
                                    print("Rate limit hit, slowing down...")
                                    await asyncio.sleep(1)
                        except Exception as e:
                            print(f"Error fetching routes for commodity {commodity_name} ({commodity_id}): {e}")
                            continue

                    # Add small delay to avoid rate limiting
                    if i % 10 == 0 and i > 0:
                        await asyncio.sleep(0.5)

            # Sort by profit descending to get the best routes first
            all_routes.sort(key=lambda x: x.get('profit', 0), reverse=True)

            print(f"Found {len(all_routes)} total routes matching criteria")
            return all_routes

        except Exception as e:
            print(f"Error fetching routes for investment {investment}: {e}")
        return []

    @app_commands.command(name="traderoute", description="Find the best trade route for your ship and investment")
    @app_commands.describe(
        ship="Select your ship",
        investment="Your investment amount in UEC"
    )
    async def find_trade_route(self, interaction: discord.Interaction, ship: str, investment: int):
        await interaction.response.defer()
        ships = self.parse_vehicles()

        if not ships:
            await interaction.followup.send("Ship data not available. Please run `/refreshdata` first.", ephemeral=True)
            return

        selected_ship = next((s for s in ships if s['name'].lower() == ship.lower()), None)
        if not selected_ship:
            await interaction.followup.send(f"Ship '{ship}' not found. Use `/ships` to see available ships.", ephemeral=True)
            return

        if selected_ship['scu'] == 0:
            await interaction.followup.send(f"The {selected_ship['name']} has no cargo capacity. Choose a cargo ship.", ephemeral=True)
            return

        max_cargo = selected_ship['scu']

        # Fetch routes specifically for this investment amount
        routes = await self.fetch_routes_for_investment(investment, max_cargo)

        if not routes:
            await interaction.followup.send("No trade routes found for your investment amount. The UEX API might be unavailable or there are no profitable routes.", ephemeral=True)
            return

        best_routes = []
        for route in routes:
            # Use the data directly from the API response
            commodity_name = route.get('commodity_name', route.get('commodity_code', 'Unknown'))
            origin_name = route.get('origin_terminal_name', 'Unknown')
            dest_name = route.get('destination_terminal_name', 'Unknown')
            profit = route.get('profit', 0)
            price_origin = route.get('price_origin', 0)
            price_destination = route.get('price_destination', 0)
            route_investment = route.get('investment', 0)
            distance = route.get('distance', 0)
            scu_needed = route.get('scu_needed', 0)

            if profit > 0 and price_origin > 0 and route_investment > 0:
                # Calculate actual quantities based on our constraints
                max_quantity_by_investment = min(route_investment, investment) / price_origin
                max_quantity_by_cargo = max_cargo
                actual_quantity = min(max_quantity_by_investment, max_quantity_by_cargo)

                if actual_quantity > 0:
                    total_cost = actual_quantity * price_origin
                    total_revenue = actual_quantity * price_destination
                    actual_profit = total_revenue - total_cost
                    roi = (actual_profit / total_cost) * 100 if total_cost > 0 else 0

                    best_routes.append({
                        'commodity': commodity_name,
                        'quantity': int(actual_quantity),
                        'buy_location': origin_name,
                        'sell_location': dest_name,
                        'total_cost': int(total_cost),
                        'total_revenue': int(total_revenue),
                        'profit': int(actual_profit),
                        'roi': roi,
                        'distance': distance / 1000000 if distance and distance > 0 else 0,  # Convert from GM to km
                        'scu_needed': scu_needed
                    })

        if not best_routes:
            await interaction.followup.send("No profitable trade routes found for your ship and investment amount.", ephemeral=True)
            return

        # Sort by profit, then by ROI
        best_routes.sort(key=lambda x: (x['profit'], x['roi']), reverse=True)

        embed = discord.Embed(
            title=f"Best Trade Routes for {selected_ship['name']}",
            description=f"Investment: {self.format_price(investment)} UEC | Cargo: {max_cargo} SCU",
            color=discord.Color.green()
        )

        for i, route in enumerate(best_routes[:5], 1):
            distance_text = f" | Distance: {route['distance']:.0f}km" if route['distance'] and route['distance'] > 0 else ""
            embed.add_field(
                name=f"#{i} {route['commodity']}",
                value=f"**Buy:** {route['buy_location']}\n"
                      f"**Sell:** {route['sell_location']}\n"
                      f"**Quantity:** {route['quantity']} SCU\n"
                      f"**Cost:** {self.format_price(route['total_cost'])} UEC\n"
                      f"**Revenue:** {self.format_price(route['total_revenue'])} UEC\n"
                      f"**Profit:** {self.format_price(route['profit'])} UEC ({route['roi']:.1f}% ROI){distance_text}",
                inline=False
            )

        embed.set_footer(text=f"Last updated: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')} UTC")
        await interaction.followup.send(embed=embed)

    @find_trade_route.autocomplete('ship')
    async def traderoute_ship_autocomplete(self, interaction: discord.Interaction, current: str):
        try:
            ships = self.parse_vehicles()
            if not ships:
                # Return some default ships if no data is available
                return [
                    app_commands.Choice(name="Cutlass Black", value="Cutlass Black"),
                    app_commands.Choice(name="Freelancer", value="Freelancer"),
                    app_commands.Choice(name="890 Jump", value="890 Jump")
                ]

            cargo_ships = [s for s in ships if s.get('scu', 0) > 0]
            ship_names = [s['name'] for s in cargo_ships if 'name' in s]

            if not current:
                # Return top ships if no search term
                return [app_commands.Choice(name=name, value=name) for name in ship_names[:25]]

            current_lower = current.lower()
            filtered = [name for name in ship_names if current_lower in name.lower()][:25]

            # If no matches, return all ships (user might be typing something new)
            if not filtered:
                filtered = ship_names[:25]

            return [app_commands.Choice(name=name, value=name) for name in filtered]
        except Exception as e:
            print(f"Error in ship autocomplete: {e}")
            # Return some fallback options
            return [
                app_commands.Choice(name="Cutlass Black", value="Cutlass Black"),
                app_commands.Choice(name="Freelancer", value="Freelancer"),
                app_commands.Choice(name="890 Jump", value="890 Jump"),
                app_commands.Choice(name="Hull C", value="Hull C"),
                app_commands.Choice(name="Caterpillar", value="Caterpillar")
            ]

    @app_commands.command(name="refreshdata", description="Refresh all data from UEX API")
    @app_commands.checks.has_permissions(administrator=True)
    async def refresh_data(self, interaction: discord.Interaction):
        try:
            # Defer the response first
            await interaction.response.defer()

            # Perform the data refresh
            await self.update_data_extract()

            # Get counts for confirmation
            commodities_count = len(self.get_commodities())
            routes_count = len(self.get_routes())
            vehicles_count = len(self.get_vehicles())
            terminals_count = len(self.get_terminals())

            # Send the followup message
            await interaction.followup.send(
                f"✅ Data refreshed successfully from UEX API!\n"
                f"📦 Commodities: {commodities_count}\n"
                f"🚀 Routes: {routes_count}\n"
                f"🚢 Vehicles: {vehicles_count}\n"
                f"🏢 Terminals: {terminals_count}"
            )
        except Exception as e:
            # Handle the case where defer failed
            try:
                await interaction.followup.send(f"❌ Failed to refresh data: {e}")
            except:
                await interaction.response.send_message(f"❌ Failed to refresh data: {e}", ephemeral=True)



async def setup(bot):
    await bot.add_cog(TradeRoutes(bot)) 