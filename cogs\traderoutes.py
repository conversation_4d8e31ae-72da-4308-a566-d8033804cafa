import discord
from discord.ext import commands, tasks
from discord import app_commands
import os
import aiohttp
import asyncio
import re
import json
from datetime import datetime, timedelta
from discord.ui import View, Button

CACHE_TTL = timedelta(hours=6)
DATA_DIR = 'data'
PRICE_FILE = os.path.join(DATA_DIR, 'commodities_prices.txt')
ROUTES_FILE = os.path.join(DATA_DIR, 'commodities_routes.txt')
VEHICLES_FILE = os.path.join(DATA_DIR, 'vehicles.txt')
LOCATIONS_FILE = os.path.join(DATA_DIR, 'locations.txt')

UEX_API_BASE = "https://api.uexcorp.space/2.0"

class PaginatedView(View):
    def __init__(self, get_embed, total_pages, initial_page=1, timeout=180):
        super().__init__(timeout=timeout)
        self.get_embed = get_embed
        self.total_pages = total_pages
        self.current_page = initial_page
        self.add_item(Button(label="◀", custom_id="prev", style=discord.ButtonStyle.primary, disabled=initial_page == 1))
        self.add_item(Button(label="▶", custom_id="next", style=discord.ButtonStyle.primary, disabled=initial_page == total_pages))

    async def update_buttons(self, interaction: discord.Interaction):
        prev_button = self.children[0]
        next_button = self.children[1]
        prev_button.disabled = self.current_page == 1
        next_button.disabled = self.current_page == self.total_pages
        embed = self.get_embed(self.current_page)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_button_click(self, interaction: discord.Interaction, button: Button):
        if button.custom_id == "prev" and self.current_page > 1:
            self.current_page -= 1
        elif button.custom_id == "next" and self.current_page < self.total_pages:
            self.current_page += 1
        await self.update_buttons(interaction)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # Allow only the user who invoked the command to interact
        return True

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True

@commands.Cog.listener()
async def on_interaction(interaction: discord.Interaction):
    if interaction.type.name == "component":
        view = interaction.message.components[0] if interaction.message.components else None
        if isinstance(view, PaginatedView):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)

class TradeRoutes(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.last_update = None
        self.update_data_extract.start()

    def cog_unload(self):
        self.update_data_extract.cancel()

    @tasks.loop(hours=6)
    async def update_data_extract(self):
        await self.fetch_and_cache_extract('commodities_prices', PRICE_FILE)
        await self.fetch_and_cache_extract('commodities_routes', ROUTES_FILE)
        await self.fetch_and_cache_vehicles()
        await self.fetch_and_cache_locations()
        self.last_update = datetime.utcnow().isoformat()
        print(f"Updated UEX data extract at {self.last_update}")

    async def cog_load(self):
        await self.update_data_extract()

    async def fetch_and_cache_extract(self, data_type, file_path):
        """Fetch data from UEX data_extract endpoint"""
        url = f"{UEX_API_BASE}/data_extract?data={data_type}"
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    text = await response.text()
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(text)
                else:
                    print(f"Failed to fetch {data_type}: {response.status}")

    async def fetch_and_cache_vehicles(self):
        """Fetch vehicles from the JSON API endpoint"""
        url = f"{UEX_API_BASE}/vehicles"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        
                        # Handle the response format: {"status": "ok", "data": [...]}
                        if isinstance(response_data, dict) and 'data' in response_data:
                            vehicles_data = response_data['data']
                        else:
                            vehicles_data = response_data
                        
                        # Convert to UEX format for consistency
                        formatted_data = ["UEX", "Ships and Vehicles", f"Updated {datetime.now().strftime('%b %d, %Y')}"]
                        
                        spaceships = 0
                        for vehicle in vehicles_data:
                            if isinstance(vehicle, dict) and vehicle.get('is_spaceship', 0) == 1:  # Only spaceships
                                name = vehicle.get('name', 'Unknown')
                                scu = vehicle.get('scu', 0)
                                ship_class = self.get_ship_class(vehicle)
                                formatted_data.append(f"{name}: {scu} SCU, Class {ship_class}")
                                spaceships += 1
                        
                        formatted_data.append("All values are estimated")
                        
                        # Write in UEX format
                        with open(VEHICLES_FILE, 'w', encoding='utf-8') as f:
                            f.write(" • ".join(formatted_data))
                        
                        print(f"Successfully cached {spaceships} spaceships")
                    else:
                        print(f"Failed to fetch vehicles: {response.status}")
        except Exception as e:
            print(f"Error fetching vehicles: {e}")

    async def fetch_and_cache_locations(self):
        """Fetch locations from multiple JSON API endpoints"""
        location_endpoints = [
            f"{UEX_API_BASE}/cities",
            f"{UEX_API_BASE}/outposts", 
            f"{UEX_API_BASE}/space_stations",
            f"{UEX_API_BASE}/terminals"
        ]
        
        all_locations = []
        
        try:
            async with aiohttp.ClientSession() as session:
                for endpoint in location_endpoints:
                    try:
                        async with session.get(endpoint) as response:
                            if response.status == 200:
                                response_data = await response.json()
                                
                                # Handle the response format: {"status": "ok", "data": [...]}
                                if isinstance(response_data, dict) and 'data' in response_data:
                                    locations = response_data['data']
                                else:
                                    locations = response_data
                                
                                loc_type = self.get_location_type(endpoint)
                                for location in locations:
                                    if isinstance(location, dict):
                                        name = location.get('name', 'Unknown')
                                        all_locations.append(f"{name} ({loc_type})")
                    except Exception as e:
                        print(f"Error fetching from {endpoint}: {e}")
                
                # Convert to UEX format
                formatted_data = ["UEX", "Locations and Trading Posts", f"Updated {datetime.now().strftime('%b %d, %Y')}"]
                formatted_data.extend(all_locations)
                formatted_data.append("All values are estimated")
                
                # Write in UEX format
                with open(LOCATIONS_FILE, 'w', encoding='utf-8') as f:
                    f.write(" • ".join(formatted_data))
                
                print(f"Successfully cached {len(all_locations)} locations")
        except Exception as e:
            print(f"Error fetching locations: {e}")

    def get_ship_class(self, vehicle):
        """Determine ship class based on vehicle properties"""
        if vehicle.get('is_carrier', 0) == 1:
            return 5
        elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 100:
            return 4
        elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 50:
            return 3
        elif vehicle.get('is_cargo', 0) == 1:
            return 2
        else:
            return 1

    def get_location_type(self, endpoint):
        """Determine location type based on endpoint"""
        if 'cities' in endpoint:
            return 'City'
        elif 'outposts' in endpoint:
            return 'Surface Outpost'
        elif 'space_stations' in endpoint:
            return 'Space Station'
        elif 'terminals' in endpoint:
            return 'Terminal'
        else:
            return 'Unknown'

    def parse_commodities_prices(self):
        """Parse UEX data extract format for commodity prices"""
        if not os.path.exists(PRICE_FILE):
            return []
        
        try:
            with open(PRICE_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content or content == "invalid_data_input":
                return []
            
            items = content.split(" • ")
            data_items = items[3:] if len(items) > 3 else items
            
            prices = []
            for item in data_items:
                if ':' in item and 'UEC' in item:
                    try:
                        commodity, rest = item.split(':', 1)
                        commodity = commodity.strip()
                        
                        buy_match = re.search(r'Buy\s+([0-9,]+)', rest)
                        sell_match = re.search(r'Sell\s+([0-9,]+)', rest)
                        
                        if buy_match and sell_match:
                            buy_price = int(buy_match.group(1).replace(',', ''))
                            sell_price = int(sell_match.group(1).replace(',', ''))
                            
                            prices.append({
                                'commodity': commodity,
                                'buy_price': buy_price,
                                'sell_price': sell_price,
                                'avg_price': (buy_price + sell_price) // 2 if buy_price > 0 and sell_price > 0 else max(buy_price, sell_price)
                            })
                    except Exception as e:
                        print(f"Error parsing commodity item '{item}': {e}")
                        continue
            
            return prices
            
        except Exception as e:
            print(f"Error reading commodity prices file: {e}")
            return []

    def parse_vehicles(self):
        """Parse UEX data extract format for vehicles/ships"""
        if not os.path.exists(VEHICLES_FILE):
            return []
        
        try:
            with open(VEHICLES_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content or content == "invalid_data_input":
                return []
            
            items = content.split(" • ")
            data_items = items[3:] if len(items) > 3 else items
            
            ships = []
            for item in data_items:
                if ':' in item and 'SCU' in item:
                    try:
                        name, rest = item.split(':', 1)
                        name = name.strip()
                        
                        scu_match = re.search(r'(\d+)\s+SCU', rest)
                        class_match = re.search(r'Class\s+(\d+)', rest)
                        
                        if scu_match:
                            scu = int(scu_match.group(1))
                            ship_class = int(class_match.group(1)) if class_match else 1
                            
                            ships.append({
                                'name': name,
                                'scu': scu,
                                'class': ship_class
                            })
                    except Exception as e:
                        print(f"Error parsing ship item '{item}': {e}")
                        continue
            
            return ships
            
        except Exception as e:
            print(f"Error reading vehicles file: {e}")
            return []

    def parse_routes(self):
        """Parse UEX data extract format for trade routes"""
        if not os.path.exists(ROUTES_FILE):
            return []
        
        try:
            with open(ROUTES_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content or content == "invalid_data_input":
                return []
            
            items = content.split(" • ")
            data_items = items[3:] if len(items) > 3 else items
            
            routes = []
            for item in data_items:
                if ':' in item and '▶' in item and 'UEC' in item:
                    try:
                        commodity, rest = item.split(':', 1)
                        commodity = commodity.strip()
                        
                        route_part, profit_part = rest.split('=', 1)
                        buy_location, sell_location = route_part.split('▶')
                        
                        profit_match = re.search(r'Up to\s+([0-9,]+)([MK])?\s+UEC', profit_part)
                        if profit_match:
                            profit_amount = int(profit_match.group(1).replace(',', ''))
                            if profit_match.group(2) == 'M':
                                profit_amount *= 1_000_000
                            elif profit_match.group(2) == 'K':
                                profit_amount *= 1_000
                            
                            routes.append({
                                'commodity': commodity,
                                'buy_location': buy_location.strip(),
                                'sell_location': sell_location.strip(),
                                'profit': profit_amount
                            })
                    except Exception as e:
                        print(f"Error parsing route item '{item}': {e}")
                        continue
            
            return routes
            
        except Exception as e:
            print(f"Error reading routes file: {e}")
            return []

    def parse_locations(self):
        """Parse UEX data extract format for locations"""
        if not os.path.exists(LOCATIONS_FILE):
            return []
        
        try:
            with open(LOCATIONS_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content or content == "invalid_data_input":
                return []
            
            items = content.split(" • ")
            data_items = items[3:] if len(items) > 3 else items
            
            locations = []
            for item in data_items:
                if item.strip():
                    name = item.strip()
                    loc_type = 'Unknown'
                    
                    if '(' in name and ')' in name:
                        loc_type = name[name.find('(')+1:name.find(')')]
                        name = name[:name.find('(')].strip()
                    
                    locations.append({
                        'name': name,
                        'type': loc_type
                    })
            
            return locations
            
        except Exception as e:
            print(f"Error reading locations file: {e}")
            return []

    def format_price(self, price):
        if price is None or price == 0:
            return "N/A"
        if price >= 1_000_000:
            return f"{price // 1_000_000}M UEC"
        elif price >= 1_000:
            return f"{price // 1_000}K UEC"
        else:
            return f"{price} UEC"

    @app_commands.command(name="ships", description="List available ships and their cargo capacities.")
    async def list_ships(self, interaction: discord.Interaction, page: int = 1):
        ships = self.parse_vehicles()
        if not ships:
            await interaction.response.send_message("No ship data available. Please check if the data file is valid.", ephemeral=True)
            return
        cargo_ships = [s for s in ships if s['scu'] > 0]
        if not cargo_ships:
            await interaction.response.send_message("No cargo ships found in the data.", ephemeral=True)
            return
        per_page = 25
        total_pages = (len(cargo_ships) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Available Cargo Ships (Page {page}/{total_pages})",
                color=discord.Color.blue()
            )
            for ship in cargo_ships[start:end]:
                embed.add_field(
                    name=ship['name'],
                    value=f"SCU: {ship['scu']} | Class: {ship['class']}",
                    inline=True
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        message = await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="commodities", description="List available commodities and their prices.")
    async def list_commodities(self, interaction: discord.Interaction, page: int = 1):
        prices = self.parse_commodities_prices()
        if not prices:
            await interaction.response.send_message("No commodity price data available. Please check if the data file is valid.", ephemeral=True)
            return
        per_page = 10
        total_pages = (len(prices) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Commodities (Page {page}/{total_pages})",
                color=discord.Color.green()
            )
            for p in prices[start:end]:
                embed.add_field(
                    name=p['commodity'],
                    value=f"Buy: {self.format_price(p['buy_price'])}\nSell: {self.format_price(p['sell_price'])}",
                    inline=False
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        message = await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="routes", description="List top trade routes.")
    async def list_routes(self, interaction: discord.Interaction, page: int = 1):
        routes = self.parse_routes()
        if not routes:
            await interaction.response.send_message("No trade route data available. Please check if the data file is valid.", ephemeral=True)
            return
        sorted_routes = sorted(routes, key=lambda x: x['profit'], reverse=True)
        per_page = 10
        total_pages = (len(sorted_routes) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Top Trade Routes (Page {page}/{total_pages})",
                color=discord.Color.gold()
            )
            for r in sorted_routes[start:end]:
                embed.add_field(
                    name=f"{r['commodity']}",
                    value=f"Buy: {r['buy_location']}\nSell: {r['sell_location']}\nProfit: {self.format_price(r['profit'])}",
                    inline=False
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        message = await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="locations", description="List all known locations.")
    async def list_locations(self, interaction: discord.Interaction, page: int = 1):
        locations = self.parse_locations()
        if not locations:
            await interaction.response.send_message("No location data available. Please check if the data file is valid.", ephemeral=True)
            return
        
        per_page = 25
        total_pages = (len(locations) + per_page - 1) // per_page
        page = max(1, min(page, total_pages))
        start = (page - 1) * per_page
        end = start + per_page
        
        embed = discord.Embed(
            title=f"Locations (Page {page}/{total_pages})",
            color=discord.Color.purple()
        )
        
        for loc in locations[start:end]:
            embed.add_field(
                name=loc['name'], 
                value=f"Type: {loc['type']}", 
                inline=True
            )
        
        if self.last_update:
            embed.set_footer(text=f"Last updated: {self.last_update}")
        
        await interaction.response.send_message(embed=embed)

    @app_commands.command(name="traderoute", description="Find the best trade route for your ship and investment")
    @app_commands.describe(
        ship="Select your ship",
        investment="Your investment amount in UEC"
    )
    async def find_trade_route(self, interaction: discord.Interaction, ship: str, investment: int):
        await interaction.response.defer()
        ships = self.parse_vehicles()
        routes = self.parse_routes()
        if not ships or not routes:
            await interaction.followup.send("Required data not available. Please check if all data files are valid.", ephemeral=True)
            return
        selected_ship = next((s for s in ships if s['name'].lower() == ship.lower()), None)
        if not selected_ship:
            await interaction.followup.send(f"Ship '{ship}' not found. Use `/ships` to see available ships.", ephemeral=True)
            return
        if selected_ship['scu'] == 0:
            await interaction.followup.send(f"The {selected_ship['name']} has no cargo capacity. Choose a cargo ship.", ephemeral=True)
            return
        max_cargo = selected_ship['scu']
        best_routes = []
        for route in routes:
            commodity = route['commodity']
            buy_location = route['buy_location']
            sell_location = route['sell_location']
            # Use price_origin and price_destination from the route data if available
            price_origin = route.get('price_origin')
            price_destination = route.get('price_destination')
            if not (isinstance(price_origin, (int, float)) and isinstance(price_destination, (int, float)) and price_origin > 0 and price_destination > 0):
                continue
            # Calculate how much of this commodity we can buy
            max_quantity = min(max_cargo, investment // price_origin)
            if max_quantity <= 0:
                continue
            total_cost = max_quantity * price_origin
            total_revenue = max_quantity * price_destination
            profit = total_revenue - total_cost
            if profit > 0:
                best_routes.append({
                    'commodity': commodity,
                    'quantity': max_quantity,
                    'buy_location': buy_location,
                    'sell_location': sell_location,
                    'total_cost': total_cost,
                    'total_revenue': total_revenue,
                    'profit': profit,
                    'roi': (profit / total_cost) * 100 if total_cost > 0 else 0
                })
        if not best_routes:
            await interaction.followup.send("No profitable trade routes found for your ship and investment amount.", ephemeral=True)
            return
        best_routes.sort(key=lambda x: x['profit'], reverse=True)
        embed = discord.Embed(
            title=f"Best Trade Routes for {selected_ship['name']}",
            description=f"Investment: {self.format_price(investment)} | Cargo: {max_cargo} SCU",
            color=discord.Color.green()
        )
        for i, route in enumerate(best_routes[:5], 1):
            embed.add_field(
                name=f"#{i} {route['commodity']}",
                value=f"Buy: {route['buy_location']}\nSell: {route['sell_location']}\n"
                      f"Quantity: {route['quantity']} SCU\n"
                      f"Cost: {self.format_price(route['total_cost'])}\n"
                      f"Revenue: {self.format_price(route['total_revenue'])}\n"
                      f"Profit: {self.format_price(route['profit'])} ({route['roi']:.1f}% ROI)",
                inline=False
            )
        if self.last_update:
            embed.set_footer(text=f"Last updated: {self.last_update}")
        await interaction.followup.send(embed=embed)

    @find_trade_route.autocomplete('ship')
    async def traderoute_ship_autocomplete(self, interaction: discord.Interaction, current: str):
        ships = self.parse_vehicles()
        cargo_ships = [s for s in ships if s['scu'] > 0]
        ship_names = [s['name'] for s in cargo_ships]
        current_lower = current.lower()
        filtered = [name for name in ship_names if current_lower in name.lower()][:25]
        return [app_commands.Choice(name=name, value=name) for name in filtered]

    @app_commands.command(name="refreshdata", description="Refresh all data from UEX API")
    @app_commands.checks.has_permissions(administrator=True)
    async def refresh_data(self, interaction: discord.Interaction):
        try:
            # Defer the response first
            await interaction.response.defer()
            
            # Perform the data refresh
            await self.update_data_extract()
            
            # Send the followup message
            await interaction.followup.send("Data refreshed successfully from UEX API.")
        except Exception as e:
            # Handle the case where defer failed
            try:
                await interaction.followup.send(f"Failed to refresh data: {e}")
            except:
                await interaction.response.send_message(f"Failed to refresh data: {e}", ephemeral=True)

    @app_commands.command(name="update_uex_data", description="Fetch and update trade routes and prices from UEX API (admin only)")
    @app_commands.checks.has_permissions(administrator=True)
    async def update_uex_data(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        debug_msgs = []
        try:
            async with aiohttp.ClientSession() as session:
                # Fetch all commodities to get their IDs and names
                async with session.get('https://api.uexcorp.space/2.0/commodities') as resp:
                    commodities_raw = await resp.text()
                    if resp.status != 200:
                        await interaction.followup.send(f"Failed to fetch commodities: {resp.status}\nRaw response: {commodities_raw}", ephemeral=True)
                        return
                    commodities_data = await resp.json()
                debug_msgs.append(f"Raw commodities API response: {commodities_raw[:900]}")
                commodity_list = commodities_data.get('data', [])
                commodity_ids = [c['id'] for c in commodity_list if 'id' in c]
                commodity_id_to_name = {c['id']: c['name'] for c in commodity_list if 'id' in c and 'name' in c}
                debug_msgs.append(f"Commodity IDs: {commodity_ids[:10]}")
                if not commodity_ids:
                    await interaction.followup.send("No commodity IDs found. See debug info below:", ephemeral=True)
                    await interaction.followup.send("\n".join(debug_msgs)[:1900], ephemeral=True)
                    return

                # Fetch all terminals to map terminal IDs to names
                async with session.get('https://api.uexcorp.space/2.0/terminals') as resp:
                    terminals_raw = await resp.text()
                    if resp.status != 200:
                        await interaction.followup.send(f"Failed to fetch terminals: {resp.status}\nRaw response: {terminals_raw}", ephemeral=True)
                        return
                    terminals_data = await resp.json()
                terminal_list = terminals_data.get('data', [])
                terminal_id_to_name = {t['id']: t['name'] for t in terminal_list if 'id' in t and 'name' in t}

                # Aggregate all routes for all commodities, log first 3
                all_routes = []
                for idx, cid in enumerate(commodity_ids):
                    async with session.get(f'https://api.uexcorp.space/2.0/commodities_routes?id_commodity={cid}') as resp:
                        if resp.status == 200:
                            routes = await resp.json()
                            if idx < 3:
                                debug_msgs.append(f"Routes for commodity ID {cid}: {routes}")
                            if isinstance(routes, dict) and 'data' in routes:
                                all_routes.extend(routes['data'])
                            elif isinstance(routes, list):
                                all_routes.extend(routes)
                if not all_routes:
                    debug_msgs.append("No routes found for any commodity.")

                # Aggregate all prices for all commodities, log first 3 (raw JSON)
                all_prices = []
                for idx, cid in enumerate(commodity_ids):
                    async with session.get(f'https://api.uexcorp.space/2.0/commodities_prices?id_commodity={cid}') as resp:
                        raw_price = await resp.text()
                        if idx < 3:
                            debug_msgs.append(f"Raw price response for commodity ID {cid}: {raw_price}")
                        if resp.status == 200:
                            prices = await resp.json()
                            if isinstance(prices, dict) and 'data' in prices:
                                all_prices.extend(prices['data'])
                            elif isinstance(prices, list):
                                all_prices.extend(prices)
                if not all_prices:
                    debug_msgs.append("No prices found for any commodity.")

            # Parse and write routes (robust, parser-compatible, human-readable)
            routes_txt = ["UEX  •  Best Commodities Routes  •  Updated (API)  •"]
            valid_routes = 0
            for route in all_routes:
                cid = route.get('id_commodity')
                origin_id = route.get('id_terminal_origin')
                dest_id = route.get('id_terminal_destination')
                profit = route.get('profit')
                if not (cid and origin_id and dest_id and isinstance(profit, (int, float))):
                    continue
                commodity = commodity_id_to_name.get(cid, f"ID{cid}")
                origin = terminal_id_to_name.get(origin_id, f"ID{origin_id}")
                destination = terminal_id_to_name.get(dest_id, f"ID{dest_id}")
                if profit >= 1_000_000:
                    profit_str = f"{int(profit/1_000_000)}M UEC"
                elif profit >= 1_000:
                    profit_str = f"{int(profit/1_000)}K UEC"
                else:
                    profit_str = f"{int(profit)} UEC"
                routes_txt.append(f"{commodity}: {origin} ▶ {destination} = Up to {profit_str}")
                valid_routes += 1
            routes_txt.append("All values are estimated")
            with open('data/commodities_routes.txt', 'w', encoding='utf-8') as f:
                f.write('  •  '.join(routes_txt))

            # Parse and write prices (robust, parser-compatible, human-readable)
            prices_txt = ["UEX  •  Commodities Prices (Average)  •  Updated (API)  •"]
            valid_prices = 0
            for price in all_prices:
                cid = price.get('id_commodity')
                buy = price.get('price_buy')
                sell = price.get('price_sell')
                if not (cid and isinstance(buy, (int, float)) and isinstance(sell, (int, float)) and buy > 0 and sell > 0):
                    continue
                commodity = commodity_id_to_name.get(cid, f"ID{cid}")
                prices_txt.append(f"{commodity}: Buy {int(buy):,}, Sell {int(sell):,} UEC/SCU")
                valid_prices += 1
            prices_txt.append("All values are estimated")
            with open('data/commodities_prices.txt', 'w', encoding='utf-8') as f:
                f.write('  •  '.join(prices_txt))

            await interaction.followup.send(f"Successfully updated UEX trade routes and prices from API!\nRoutes: {valid_routes}, Prices: {valid_prices}", ephemeral=True)
            await interaction.followup.send("\n".join(debug_msgs)[:1900], ephemeral=True)
        except Exception as e:
            await interaction.followup.send(f"Failed to update UEX data: {e}", ephemeral=True)

async def setup(bot):
    await bot.add_cog(TradeRoutes(bot)) 