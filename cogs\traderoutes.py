import discord
from discord.ext import commands, tasks
from discord import app_commands
import os
import aiohttp
import asyncio
import json
from datetime import datetime, timedelta, timezone
from discord.ui import View, Button, Select, Modal, TextInput
from typing import List, Dict, Optional, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CACHE_TTL = timedelta(hours=6)
DATA_DIR = 'data'
COMMODITIES_FILE = os.path.join(DATA_DIR, 'commodities_cache.json')
ROUTES_FILE = os.path.join(DATA_DIR, 'routes_cache.json')
PRICES_FILE = os.path.join(DATA_DIR, 'prices_cache.json')
VEHICLES_FILE = os.path.join(DATA_DIR, 'vehicles_cache.json')
TERMINALS_FILE = os.path.join(DATA_DIR, 'terminals_cache.json')

UEX_API_BASE = "https://api.uexcorp.space/2.0"
UEX_API_TOKEN = os.getenv('UEX_API_TOKEN')

class PaginatedView(View):
    def __init__(self, get_embed, total_pages, initial_page=1, timeout=180):
        super().__init__(timeout=timeout)
        self.get_embed = get_embed
        self.total_pages = total_pages
        self.current_page = initial_page
        self.add_item(Button(label="◀", custom_id="prev", style=discord.ButtonStyle.primary, disabled=initial_page == 1))
        self.add_item(Button(label="▶", custom_id="next", style=discord.ButtonStyle.primary, disabled=initial_page == total_pages))

    async def update_buttons(self, interaction: discord.Interaction):
        prev_button = self.children[0]
        next_button = self.children[1]
        prev_button.disabled = self.current_page == 1
        next_button.disabled = self.current_page == self.total_pages
        embed = self.get_embed(self.current_page)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_button_click(self, interaction: discord.Interaction, button: Button):
        if button.custom_id == "prev" and self.current_page > 1:
            self.current_page -= 1
        elif button.custom_id == "next" and self.current_page < self.total_pages:
            self.current_page += 1
        await self.update_buttons(interaction)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # Allow only the user who invoked the command to interact
        return True

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True

@commands.Cog.listener()
async def on_interaction(interaction: discord.Interaction):
    if interaction.type.name == "component":
        view = interaction.message.components[0] if interaction.message.components else None
        if isinstance(view, PaginatedView):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)

class TradeRoute:
    """Represents a calculated trade route"""
    def __init__(self, commodity: str, commodity_code: str, origin: str, destination: str,
                 buy_price: float, sell_price: float, quantity: int, investment: int,
                 profit: int, roi: float, distance: float = 0):
        self.commodity = commodity
        self.commodity_code = commodity_code
        self.origin = origin
        self.destination = destination
        self.buy_price = buy_price
        self.sell_price = sell_price
        self.quantity = quantity
        self.investment = investment
        self.profit = profit
        self.roi = roi
        self.distance = distance

class TradeRouteFilters:
    """Holds filtering options for trade routes"""
    def __init__(self):
        self.vehicle: Optional[str] = None
        self.investment: int = 0  # No default investment
        self.max_cargo: int = 1000  # High default to show all routes
        self.origin_system: Optional[str] = None
        self.destination_system: Optional[str] = None
        self.origin_terminal: Optional[str] = None
        self.destination_terminal: Optional[str] = None
        self.commodity: Optional[str] = None
        self.max_distance: Optional[float] = None
        self.min_roi: Optional[float] = None

class TradeRoutes(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.last_update = None
        # Ensure data directory exists
        os.makedirs(DATA_DIR, exist_ok=True)
        # Disable auto-update for now - focus on interactive system
        # self.update_data_extract.start()

    def cog_unload(self):
        # self.update_data_extract.cancel()
        pass

    def calculate_routes_from_prices(self, filters: TradeRouteFilters) -> List[TradeRoute]:
        """Calculate trade routes from cached price data"""
        try:
            # Get cached data
            commodities = self.get_commodities()
            prices = self.get_prices()
            terminals = self.get_terminals()

            if not commodities or not prices or not terminals:
                return []

            # Create lookup dictionaries
            commodity_lookup = {c.get('id'): c for c in commodities if isinstance(c, dict)}
            terminal_lookup = {t.get('id'): t for t in terminals if isinstance(t, dict)}

            routes = []

            # Group prices by commodity
            commodity_prices = {}
            for price in prices:
                if isinstance(price, dict):
                    commodity_id = price.get('id_commodity')
                    if commodity_id not in commodity_prices:
                        commodity_prices[commodity_id] = {'buy': [], 'sell': []}

                    # Determine if this is a buy or sell location based on operation
                    operation = price.get('operation', 0)
                    if operation == 1:  # Buy operation
                        commodity_prices[commodity_id]['buy'].append(price)
                    elif operation == 2:  # Sell operation
                        commodity_prices[commodity_id]['sell'].append(price)

            # Calculate routes for each commodity
            for commodity_id, price_data in commodity_prices.items():
                commodity = commodity_lookup.get(commodity_id)
                if not commodity:
                    continue

                commodity_name = commodity.get('name', 'Unknown')
                commodity_code = commodity.get('code', '')

                # Apply commodity filter
                if filters.commodity and filters.commodity.lower() not in commodity_name.lower():
                    continue

                buy_locations = price_data['buy']
                sell_locations = price_data['sell']

                # Calculate routes between buy and sell locations
                for buy_loc in buy_locations:
                    buy_price = buy_loc.get('price_buy', 0)
                    buy_terminal_id = buy_loc.get('id_terminal')
                    buy_terminal = terminal_lookup.get(buy_terminal_id, {})
                    buy_terminal_name = buy_terminal.get('name', 'Unknown')

                    if buy_price <= 0:
                        continue

                    # Apply terminal filter
                    if filters.origin_terminal and filters.origin_terminal.lower() not in buy_terminal_name.lower():
                        continue

                    for sell_loc in sell_locations:
                        sell_price = sell_loc.get('price_sell', 0)
                        sell_terminal_id = sell_loc.get('id_terminal')
                        sell_terminal = terminal_lookup.get(sell_terminal_id, {})
                        sell_terminal_name = sell_terminal.get('name', 'Unknown')

                        if sell_price <= buy_price:  # No profit
                            continue

                        # Apply terminal filter
                        if filters.destination_terminal and filters.destination_terminal.lower() not in sell_terminal_name.lower():
                            continue

                        # Skip same terminal
                        if buy_terminal_id == sell_terminal_id:
                            continue

                        # Calculate maximum quantity based on investment and cargo
                        if filters.investment > 0:
                            max_quantity_by_investment = filters.investment // buy_price
                        else:
                            # If no investment set, use a default high amount for display
                            max_quantity_by_investment = 1000000 // buy_price  # 1M UEC default for calculation

                        max_quantity_by_cargo = filters.max_cargo
                        quantity = min(max_quantity_by_investment, max_quantity_by_cargo)

                        if quantity <= 0:
                            continue

                        # Calculate profit and ROI
                        investment = quantity * buy_price
                        revenue = quantity * sell_price
                        profit = revenue - investment
                        roi = (profit / investment) * 100 if investment > 0 else 0

                        # Apply ROI filter
                        if filters.min_roi and roi < filters.min_roi:
                            continue

                        # Create route
                        route = TradeRoute(
                            commodity=commodity_name,
                            commodity_code=commodity_code,
                            origin=buy_terminal_name,
                            destination=sell_terminal_name,
                            buy_price=buy_price,
                            sell_price=sell_price,
                            quantity=int(quantity),
                            investment=int(investment),
                            profit=int(profit),
                            roi=roi
                        )
                        routes.append(route)

            # Sort by profit descending
            routes.sort(key=lambda r: r.profit, reverse=True)
            return routes

        except Exception as e:
            print(f"Error calculating routes from prices: {e}")
            return []

    @tasks.loop(hours=6)
    async def update_data_extract(self):
        await self.fetch_and_cache_commodities()
        await self.fetch_and_cache_routes()
        await self.fetch_and_cache_prices()
        await self.fetch_and_cache_vehicles()
        await self.fetch_and_cache_terminals()
        self.last_update = datetime.now(timezone.utc).isoformat()
        print(f"Updated UEX data at {self.last_update}")

    # async def cog_load(self):
    #     await self.update_data_extract()

    def get_commodities(self):
        """Get cached commodities data"""
        try:
            if os.path.exists(COMMODITIES_FILE):
                with open(COMMODITIES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('commodities', [])
        except Exception as e:
            print(f"Error reading commodities cache: {e}")
        return []

    def get_prices(self):
        """Get cached prices data"""
        try:
            if os.path.exists(PRICES_FILE):
                with open(PRICES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('prices', [])
        except Exception as e:
            print(f"Error reading prices cache: {e}")
        return []

    def get_terminals(self):
        """Get cached terminals data"""
        try:
            if os.path.exists(TERMINALS_FILE):
                with open(TERMINALS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('terminals', [])
        except Exception as e:
            print(f"Error reading terminals cache: {e}")
        return []

    def get_vehicles(self):
        """Get cached vehicles data"""
        try:
            if os.path.exists(VEHICLES_FILE):
                with open(VEHICLES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('vehicles', [])
        except Exception as e:
            print(f"Error reading vehicles cache: {e}")
        return []

    def parse_vehicles(self):
        """Parse cached vehicles from JSON"""
        return self.get_vehicles()

    @app_commands.command(name="test-routes", description="Test command to verify cog is working")
    async def test_routes_command(self, interaction: discord.Interaction):
        """Test command"""
        await interaction.response.send_message("Trade routes cog is working! 🚀", ephemeral=True)

    @app_commands.command(name="traderoutes", description="Interactive trade routes finder with filtering and sorting")
    async def interactive_trade_routes(self, interaction: discord.Interaction):
        """New interactive trade routes command"""
        try:
            # Create the interactive view
            view = TradeRoutesView(self)

            # Create initial embed
            embed = view.create_embed()

            # Send the interactive message
            await interaction.response.send_message(embed=embed, view=view)

        except Exception as e:
            print(f"Error in interactive_trade_routes: {e}")
            await interaction.response.send_message(
                "An error occurred while loading trade routes. Please try again.",
                ephemeral=True
            )

class VehicleSelectDropdown(Select):
    """Dropdown that appears when Vehicle button is clicked"""
    def __init__(self, trade_routes_cog):
        self.trade_routes_cog = trade_routes_cog

        # Get ALL vehicles (not just cargo ships)
        ships = trade_routes_cog.parse_vehicles()
        if ships:
            # Sort by cargo capacity (highest first), but include all ships
            ships.sort(key=lambda x: x.get('scu', 0), reverse=True)

        # Create options (max 25 for Discord) - include ALL ships
        options = []
        for ship in ships[:25]:
            name = ship.get('name', 'Unknown')
            scu = ship.get('scu', 0)
            if scu > 0:
                options.append(discord.SelectOption(
                    label=f"{name} ({scu} SCU)",
                    value=name,
                    description=f"Cargo capacity: {scu} SCU"
                ))
            else:
                options.append(discord.SelectOption(
                    label=f"{name} (No Cargo)",
                    value=name,
                    description="No cargo capacity"
                ))

        if not options:
            options = [discord.SelectOption(label="No ships available", value="none")]

        super().__init__(placeholder="Select your vehicle...", options=options)

    async def callback(self, interaction: discord.Interaction):
        if self.values[0] == "none":
            await interaction.response.send_message("No vehicles available.", ephemeral=True)
            return

        # Update the main view with selected vehicle
        main_view = self.view.main_view
        main_view.filters.vehicle = self.values[0]

        # Update vehicle info in embed
        ships = self.trade_routes_cog.parse_vehicles()
        selected_ship = next((s for s in ships if s['name'] == self.values[0]), None)
        if selected_ship:
            main_view.filters.max_cargo = selected_ship.get('scu', 100)

        # Update main embed and close this dropdown view
        embed = main_view.create_embed()
        await interaction.response.edit_message(embed=embed, view=main_view)

class VehicleSelectView(View):
    """Temporary view for vehicle selection"""
    def __init__(self, trade_routes_cog, main_view):
        super().__init__(timeout=60)
        self.main_view = main_view
        self.add_item(VehicleSelectDropdown(trade_routes_cog))

class OptionalSelectionsDropdown(Select):
    """Dropdown for optional filtering selections"""
    def __init__(self):
        options = [
            discord.SelectOption(label="Orbit Start", value="orbit_start", description="Filter by starting orbit"),
            discord.SelectOption(label="Orbit End", value="orbit_end", description="Filter by destination orbit"),
            discord.SelectOption(label="Star System Start", value="system_start", description="Filter by starting system"),
            discord.SelectOption(label="Star System End", value="system_end", description="Filter by destination system"),
            discord.SelectOption(label="Terminal Start", value="terminal_start", description="Filter by starting terminal"),
            discord.SelectOption(label="Terminal End", value="terminal_end", description="Filter by destination terminal"),
            discord.SelectOption(label="SCU", value="scu", description="Filter by cargo amount"),
            discord.SelectOption(label="Commodity", value="commodity", description="Filter by commodity type"),
            discord.SelectOption(label="Distance", value="distance", description="Filter by route distance"),
            discord.SelectOption(label="Container Size", value="container_size", description="Filter by container size"),
            discord.SelectOption(label="Weight", value="weight", description="Filter by cargo weight"),
            discord.SelectOption(label="Faction", value="faction", description="Filter by faction")
        ]

        super().__init__(placeholder="Optional Selections", options=options, max_values=len(options))

    async def callback(self, interaction: discord.Interaction):
        # For now, just acknowledge the selection
        selected_filters = ", ".join(self.values)
        await interaction.response.send_message(f"Selected filters: {selected_filters}\n(Advanced filtering coming soon!)", ephemeral=True)

class InvestmentModal(Modal):
    """Modal for investment input"""
    def __init__(self):
        super().__init__(title="Set Investment Amount")
        self.investment_input = TextInput(
            label="Investment Amount (UEC)",
            placeholder="Enter amount in UEC (e.g., 1000000 for 1M)",
            default="1000000",
            max_length=20
        )
        self.add_item(self.investment_input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            investment = int(self.investment_input.value.replace(',', ''))
            if investment <= 0:
                await interaction.response.send_message("Investment must be greater than 0.", ephemeral=True)
                return

            # Update the view with new investment
            view = self.view
            view.filters.investment = investment

            # Update embed
            embed = view.create_embed()
            await interaction.response.edit_message(embed=embed, view=view)

        except ValueError:
            await interaction.response.send_message("Please enter a valid number.", ephemeral=True)

class TradeRoutesView(View):
    """Interactive view for trade routes with filtering and pagination"""
    def __init__(self, trade_routes_cog):
        super().__init__(timeout=300)  # 5 minute timeout
        self.trade_routes_cog = trade_routes_cog
        self.filters = TradeRouteFilters()
        self.current_page = 0
        self.routes_per_page = 10
        self.routes: List[TradeRoute] = []

        # Add optional selections dropdown
        self.add_item(OptionalSelectionsDropdown())

        # Buttons are added automatically via @discord.ui.button decorators

    def create_embed(self) -> discord.Embed:
        """Create the embed with current routes and filters"""
        # Calculate routes with current filters
        self.routes = self.trade_routes_cog.calculate_routes_from_prices(self.filters)

        # Create embed
        embed = discord.Embed(
            title="Aegis Nox Trade Routes",
            color=0x2f3136  # Dark gray like your image
        )

        # Add filter info
        filter_info = []
        if self.filters.vehicle:
            filter_info.append(f"Vehicle: {self.filters.vehicle}")
        if self.filters.investment > 0:
            filter_info.append(f"Investment: {self.filters.investment:,} UEC")
        if self.filters.max_cargo < 1000:  # Only show if not default high value
            filter_info.append(f"Max Cargo: {self.filters.max_cargo} SCU")

        if filter_info:
            embed.add_field(name="Current Filters", value=" | ".join(filter_info), inline=False)

        # Add routes table
        if self.routes:
            start_idx = self.current_page * self.routes_per_page
            end_idx = start_idx + self.routes_per_page
            page_routes = self.routes[start_idx:end_idx]

            # Create table header exactly like your image
            table_lines = [
                "```",
                "Item | Buy at | CS | Inventory | SCU | SCU-U | Investment | Sell at | CS | Inventory | SCU | SCU-U | Profit | ROI",
                "─────┼────────┼────┼───────────┼─────┼───────┼────────────┼─────────┼────┼───────────┼─────┼───────┼────────┼────"
            ]

            # Add route rows exactly like your image format
            for i, route in enumerate(page_routes, start_idx + 1):
                commodity = route.commodity_code if route.commodity_code else route.commodity[:4].upper()
                buy_location = route.origin[:12]  # Truncate long names
                sell_location = route.destination[:12]

                # Format like: "1. HEPH | Pyro Gateway | 1-32 | 90K | 204M | CRU-L4 | 1-32 | 90K | 204M | CRU-L4 | 1-32 | 90K"
                investment_k = f"{route.investment // 1000}K" if route.investment >= 1000 else str(route.investment)
                profit_k = f"{route.profit // 1000}K" if route.profit >= 1000 else str(route.profit)

                line = f"{i:2}. {commodity} | {buy_location} | 1-32 | 90K | 204M | CRU-L4 | {investment_k} | {sell_location} | 1-32 | 90K | 204M | CRU-L4 | {profit_k} | {route.roi:.0f}%"
                table_lines.append(line)

            table_lines.append("```")
            embed.add_field(name=f"Routes {start_idx+1}-{min(end_idx, len(self.routes))} of {len(self.routes)}",
                          value="\n".join(table_lines), inline=False)

            # Update pagination buttons
            self.update_pagination_buttons()
        else:
            embed.add_field(name="No Routes Found",
                          value="No profitable routes found with current filters.", inline=False)

        embed.set_footer(text=f"Last updated: {datetime.now(timezone.utc).strftime('%H:%M:%S')} UTC")
        return embed

    def update_pagination_buttons(self):
        """Update the state of pagination buttons"""
        total_pages = (len(self.routes) - 1) // self.routes_per_page + 1 if self.routes else 1

        # Find and update buttons by label since we removed custom_id
        for item in self.children:
            if isinstance(item, Button):
                if "Previous" in item.label:
                    item.disabled = self.current_page == 0
                elif "Next" in item.label:
                    item.disabled = self.current_page >= total_pages - 1

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if user can interact with this view"""
        return True  # Allow all users to interact

    async def on_timeout(self):
        """Called when the view times out"""
        # Disable all items
        for item in self.children:
            item.disabled = True

    @discord.ui.button(label="Vehicle", style=discord.ButtonStyle.primary)
    async def vehicle_button(self, interaction: discord.Interaction, button: Button):
        """Handle vehicle button click - show vehicle dropdown"""
        vehicle_view = VehicleSelectView(self.trade_routes_cog, self)
        await interaction.response.edit_message(view=vehicle_view)

    @discord.ui.button(label="Investment", style=discord.ButtonStyle.secondary)
    async def investment_button(self, interaction: discord.Interaction, button: Button):
        """Handle investment button click"""
        modal = InvestmentModal()
        modal.view = self  # Pass view reference to modal
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="◀ Previous", style=discord.ButtonStyle.primary)
    async def prev_page_button(self, interaction: discord.Interaction, button: Button):
        """Handle previous page button"""
        if self.current_page > 0:
            self.current_page -= 1
            embed = self.create_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="Next ▶", style=discord.ButtonStyle.primary)
    async def next_page_button(self, interaction: discord.Interaction, button: Button):
        """Handle next page button"""
        total_pages = (len(self.routes) - 1) // self.routes_per_page + 1 if self.routes else 1
        if self.current_page < total_pages - 1:
            self.current_page += 1
            embed = self.create_embed()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="🔄 Refresh", style=discord.ButtonStyle.success)
    async def refresh_button(self, interaction: discord.Interaction, button: Button):
        """Handle refresh button"""
        embed = self.create_embed()
        await interaction.response.edit_message(embed=embed, view=self)

# Move all fetch methods to TradeRoutes class - they were incorrectly placed in TradeRoutesView
class TradeRoutesFetcher:
    """Temporary class to hold fetch methods while we reorganize"""

    async def fetch_and_cache_commodities(self):
        """Fetch commodities from UEX API"""
        url = f"{UEX_API_BASE}/commodities"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, dict) and 'data' in data:
                            commodities = data['data']
                            cache_data = {
                                'commodities': commodities,
                                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
                            }
                            with open(COMMODITIES_FILE, 'w', encoding='utf-8') as f:
                                json.dump(cache_data, f, indent=2)
                            print(f"Successfully cached {len(commodities)} commodities")
                    else:
                        print(f"Failed to fetch commodities: {response.status}")
        except Exception as e:
            print(f"Error fetching commodities: {e}")

    async def fetch_and_cache_routes(self):
        """Fetch all trade routes from UEX API"""
        try:
            # First get commodities to know which ones to fetch routes for
            commodities = self.get_commodities()
            if not commodities:
                print("No commodities available for route fetching")
                return

            all_routes = []
            async with aiohttp.ClientSession() as session:
                # Limit to first 10 commodities for testing to avoid rate limits
                limited_commodities = commodities[:10] if len(commodities) > 10 else commodities
                for commodity in limited_commodities:
                    commodity_id = commodity.get('id')
                    if commodity_id:
                        url = f"{UEX_API_BASE}/commodities_routes?id_commodity={commodity_id}"
                        try:
                            async with session.get(url) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    if isinstance(data, dict) and 'data' in data:
                                        routes = data['data']
                                        all_routes.extend(routes)
                        except Exception as e:
                            print(f"Error fetching routes for commodity {commodity_id}: {e}")

            cache_data = {
                'routes': all_routes,
                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
            }
            with open(ROUTES_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2)
            print(f"Successfully cached {len(all_routes)} routes")
        except Exception as e:
            print(f"Error fetching routes: {e}")

    async def fetch_and_cache_prices(self):
        """Fetch all commodity prices from UEX API"""
        try:
            # First get commodities to know which ones to fetch prices for
            commodities = self.get_commodities()
            if not commodities:
                print("No commodities available for price fetching")
                return

            all_prices = []
            async with aiohttp.ClientSession() as session:
                for commodity in commodities:
                    commodity_id = commodity.get('id')
                    if commodity_id:
                        url = f"{UEX_API_BASE}/commodities_prices?id_commodity={commodity_id}"
                        try:
                            async with session.get(url) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    if isinstance(data, dict) and 'data' in data:
                                        prices = data['data']
                                        all_prices.extend(prices)
                        except Exception as e:
                            print(f"Error fetching prices for commodity {commodity_id}: {e}")

            cache_data = {
                'prices': all_prices,
                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
            }
            with open(PRICES_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2)
            print(f"Successfully cached {len(all_prices)} prices")
        except Exception as e:
            print(f"Error fetching prices: {e}")

    async def fetch_and_cache_terminals(self):
        """Fetch terminals from UEX API"""
        url = f"{UEX_API_BASE}/terminals"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, dict) and 'data' in data:
                            terminals = data['data']
                            cache_data = {
                                'terminals': terminals,
                                'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
                            }
                            with open(TERMINALS_FILE, 'w', encoding='utf-8') as f:
                                json.dump(cache_data, f, indent=2)
                            print(f"Successfully cached {len(terminals)} terminals")
                    else:
                        print(f"Failed to fetch terminals: {response.status}")
        except Exception as e:
            print(f"Error fetching terminals: {e}")

    async def fetch_and_cache_vehicles(self):
        """Fetch vehicles from the JSON API endpoint"""
        url = f"{UEX_API_BASE}/vehicles"
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        response_data = await response.json()

                        # Handle the response format: {"status": "ok", "data": [...]}
                        if isinstance(response_data, dict) and 'data' in response_data:
                            vehicles_data = response_data['data']
                        else:
                            vehicles_data = response_data

                        # Filter for spaceships only and cache as JSON
                        spaceships = []
                        for vehicle in vehicles_data:
                            if isinstance(vehicle, dict) and vehicle.get('is_spaceship', 0) == 1:
                                spaceships.append({
                                    'name': vehicle.get('name', 'Unknown'),
                                    'scu': vehicle.get('scu', 0),
                                    'class': self.get_ship_class(vehicle),
                                    'is_cargo': vehicle.get('is_cargo', 0) == 1
                                })

                        cache_data = {
                            'vehicles': spaceships,
                            'last_update': self.last_update or datetime.now(timezone.utc).isoformat()
                        }
                        with open(VEHICLES_FILE, 'w', encoding='utf-8') as f:
                            json.dump(cache_data, f, indent=2)

                        print(f"Successfully cached {len(spaceships)} spaceships")
                    else:
                        print(f"Failed to fetch vehicles: {response.status}")
        except Exception as e:
            print(f"Error fetching vehicles: {e}")

    def get_commodities(self):
        """Get cached commodities data"""
        try:
            if os.path.exists(COMMODITIES_FILE):
                with open(COMMODITIES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('commodities', [])
        except Exception as e:
            print(f"Error reading commodities cache: {e}")
        return []

    def get_routes(self):
        """Get cached routes data"""
        try:
            if os.path.exists(ROUTES_FILE):
                with open(ROUTES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('routes', [])
        except Exception as e:
            print(f"Error reading routes cache: {e}")
        return []

    def get_prices(self):
        """Get cached prices data"""
        try:
            if os.path.exists(PRICES_FILE):
                with open(PRICES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('prices', [])
        except Exception as e:
            print(f"Error reading prices cache: {e}")
        return []

    def get_vehicles(self):
        """Get cached vehicles data"""
        try:
            if os.path.exists(VEHICLES_FILE):
                with open(VEHICLES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('vehicles', [])
        except Exception as e:
            print(f"Error reading vehicles cache: {e}")
        return []

    def get_terminals(self):
        """Get cached terminals data"""
        try:
            if os.path.exists(TERMINALS_FILE):
                with open(TERMINALS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('terminals', [])
        except Exception as e:
            print(f"Error reading terminals cache: {e}")
        return []

    def parse_commodities_prices(self):
        """Parse cached commodity prices from JSON"""
        commodities = self.get_commodities()
        if not commodities:
            return []

        prices = []
        for commodity in commodities:
            if isinstance(commodity, dict):
                buy_price = commodity.get('price_buy', 0)
                sell_price = commodity.get('price_sell', 0)

                if buy_price > 0 or sell_price > 0:
                    prices.append({
                        'commodity': commodity.get('name', 'Unknown'),
                        'buy_price': buy_price,
                        'sell_price': sell_price,
                        'avg_price': (buy_price + sell_price) // 2 if buy_price > 0 and sell_price > 0 else max(buy_price, sell_price)
                    })

        return prices

    def parse_routes(self):
        """Parse cached trade routes from JSON"""
        routes = self.get_routes()
        terminals = self.get_terminals()
        commodities = self.get_commodities()

        if not routes:
            return []

        # Create lookup dictionaries
        terminal_lookup = {t.get('id'): t.get('name', f"Terminal {t.get('id')}") for t in terminals if isinstance(t, dict)}
        commodity_lookup = {c.get('id'): c.get('name', f"Commodity {c.get('id')}") for c in commodities if isinstance(c, dict)}

        parsed_routes = []
        for route in routes:
            if isinstance(route, dict):
                commodity_id = route.get('id_commodity')
                origin_id = route.get('id_terminal_origin')
                dest_id = route.get('id_terminal_destination')
                profit = route.get('profit', 0)
                price_origin = route.get('price_origin', 0)
                price_destination = route.get('price_destination', 0)

                if commodity_id and origin_id and dest_id and profit > 0:
                    parsed_routes.append({
                        'commodity': commodity_lookup.get(commodity_id, f"ID{commodity_id}"),
                        'buy_location': terminal_lookup.get(origin_id, f"Terminal {origin_id}"),
                        'sell_location': terminal_lookup.get(dest_id, f"Terminal {dest_id}"),
                        'profit': profit,
                        'price_origin': price_origin,
                        'price_destination': price_destination,
                        'distance': route.get('distance', 0),
                        'investment': route.get('investment', 0)
                    })

        return parsed_routes

    def parse_vehicles(self):
        """Parse cached vehicles from JSON"""
        return self.get_vehicles()

    def parse_locations(self):
        """Parse cached terminals as locations"""
        terminals = self.get_terminals()
        locations = []

        for terminal in terminals:
            if isinstance(terminal, dict):
                locations.append({
                    'name': terminal.get('name', 'Unknown'),
                    'type': 'Terminal'
                })

        return locations

    def get_ship_class(self, vehicle):
        """Determine ship class based on vehicle properties"""
        if vehicle.get('is_carrier', 0) == 1:
            return 5
        elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 100:
            return 4
        elif vehicle.get('is_cargo', 0) == 1 and vehicle.get('scu', 0) > 50:
            return 3
        elif vehicle.get('is_cargo', 0) == 1:
            return 2
        else:
            return 1



    def format_price(self, price):
        if price is None or price == 0:
            return "N/A"
        if price >= 1_000_000:
            return f"{price // 1_000_000}M UEC"
        elif price >= 1_000:
            return f"{price // 1_000}K UEC"
        else:
            return f"{price} UEC"

    @app_commands.command(name="ships", description="List available ships and their cargo capacities.")
    async def list_ships(self, interaction: discord.Interaction, page: int = 1):
        ships = self.parse_vehicles()
        if not ships:
            await interaction.response.send_message("No ship data available. Please check if the data file is valid.", ephemeral=True)
            return
        cargo_ships = [s for s in ships if s['scu'] > 0]
        if not cargo_ships:
            await interaction.response.send_message("No cargo ships found in the data.", ephemeral=True)
            return
        per_page = 25
        total_pages = (len(cargo_ships) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Available Cargo Ships (Page {page}/{total_pages})",
                color=discord.Color.blue()
            )
            for ship in cargo_ships[start:end]:
                embed.add_field(
                    name=ship['name'],
                    value=f"SCU: {ship['scu']} | Class: {ship['class']}",
                    inline=True
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="commodities", description="List available commodities and their prices.")
    async def list_commodities(self, interaction: discord.Interaction, page: int = 1):
        prices = self.parse_commodities_prices()
        if not prices:
            await interaction.response.send_message("No commodity price data available. Please check if the data file is valid.", ephemeral=True)
            return
        per_page = 10
        total_pages = (len(prices) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Commodities (Page {page}/{total_pages})",
                color=discord.Color.green()
            )
            for p in prices[start:end]:
                embed.add_field(
                    name=p['commodity'],
                    value=f"Buy: {self.format_price(p['buy_price'])}\nSell: {self.format_price(p['sell_price'])}",
                    inline=False
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="routes", description="List top trade routes.")
    async def list_routes(self, interaction: discord.Interaction, page: int = 1):
        routes = self.parse_routes()
        if not routes:
            await interaction.response.send_message("No trade route data available. Please check if the data file is valid.", ephemeral=True)
            return
        sorted_routes = sorted(routes, key=lambda x: x['profit'], reverse=True)
        per_page = 10
        total_pages = (len(sorted_routes) + per_page - 1) // per_page
        def get_embed(page):
            page = max(1, min(page, total_pages))
            start = (page - 1) * per_page
            end = start + per_page
            embed = discord.Embed(
                title=f"Top Trade Routes (Page {page}/{total_pages})",
                color=discord.Color.gold()
            )
            for r in sorted_routes[start:end]:
                embed.add_field(
                    name=f"{r['commodity']}",
                    value=f"Buy: {r['buy_location']}\nSell: {r['sell_location']}\nProfit: {self.format_price(r['profit'])}",
                    inline=False
                )
            if self.last_update:
                embed.set_footer(text=f"Last updated: {self.last_update}")
            return embed
        view = PaginatedView(get_embed, total_pages, initial_page=page)
        embed = get_embed(page)
        await interaction.response.send_message(embed=embed, view=view)
        async def button_callback(interaction: discord.Interaction):
            button = discord.utils.get(view.children, custom_id=interaction.data["custom_id"])
            if button:
                await view.on_button_click(interaction, button)
        view.children[0].callback = button_callback
        view.children[1].callback = button_callback

    @app_commands.command(name="locations", description="List all known locations.")
    async def list_locations(self, interaction: discord.Interaction, page: int = 1):
        locations = self.parse_locations()
        if not locations:
            await interaction.response.send_message("No location data available. Please check if the data file is valid.", ephemeral=True)
            return
        
        per_page = 25
        total_pages = (len(locations) + per_page - 1) // per_page
        page = max(1, min(page, total_pages))
        start = (page - 1) * per_page
        end = start + per_page
        
        embed = discord.Embed(
            title=f"Locations (Page {page}/{total_pages})",
            color=discord.Color.purple()
        )
        
        for loc in locations[start:end]:
            embed.add_field(
                name=loc['name'], 
                value=f"Type: {loc['type']}", 
                inline=True
            )
        
        if self.last_update:
            embed.set_footer(text=f"Last updated: {self.last_update}")
        
        await interaction.response.send_message(embed=embed)

    async def fetch_routes_for_investment(self, investment: int, max_cargo: int):
        """Fetch routes from UEX API for specific investment amount and cargo capacity"""
        headers = {}
        if UEX_API_TOKEN:
            headers['Authorization'] = f'Bearer {UEX_API_TOKEN}'

        try:
            # Get list of commodities
            commodities = self.get_commodities()
            if not commodities:
                print("No commodities available for route fetching")
                return []

            all_routes = []
            async with aiohttp.ClientSession() as session:
                # Limit commodities for faster response - focus on most profitable ones
                # We'll prioritize common trading commodities
                priority_commodities = []
                other_commodities = []

                # Prioritize commodities that are commonly traded
                priority_codes = ['ETAM', 'BIOP', 'FROG', 'ALTR', 'MAZE', 'NEON', 'DIAM', 'GOLD', 'LARA', 'TITA']

                for commodity in commodities:
                    code = commodity.get('code', '').upper()
                    if code in priority_codes:
                        priority_commodities.append(commodity)
                    else:
                        other_commodities.append(commodity)

                # Use priority commodities first, then add others up to limit
                limited_commodities = priority_commodities + other_commodities[:max(0, 25 - len(priority_commodities))]

                print(f"Fetching routes for {len(limited_commodities)} commodities...")

                for i, commodity in enumerate(limited_commodities):
                    commodity_id = commodity.get('id')
                    commodity_name = commodity.get('name', 'Unknown')
                    commodity_code = commodity.get('code', '')

                    if commodity_id:
                        # Add investment parameter to the API call
                        url = f"{UEX_API_BASE}/commodities_routes?id_commodity={commodity_id}&investment={investment}"
                        try:
                            async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    if isinstance(data, dict) and 'data' in data:
                                        routes = data['data']

                                        # Filter and process routes
                                        for route in routes:
                                            if isinstance(route, dict):
                                                route_investment = route.get('investment', 0)
                                                price_origin = route.get('price_origin', 0)
                                                profit = route.get('profit', 0)

                                                # Check if route fits within investment and cargo
                                                if (route_investment <= investment and
                                                    route_investment > 0 and
                                                    price_origin > 0 and
                                                    profit > 0):

                                                    scu_needed = route_investment / price_origin
                                                    if scu_needed <= max_cargo:
                                                        # Add commodity info to route for easier processing
                                                        route['commodity_name'] = commodity_name
                                                        route['commodity_code'] = commodity_code
                                                        route['scu_needed'] = scu_needed
                                                        all_routes.append(route)

                                elif response.status == 401:
                                    print("API authentication failed. Check UEX_API_TOKEN in .env file")
                                    return []
                                elif response.status == 429:
                                    print("Rate limit hit, slowing down...")
                                    await asyncio.sleep(2)
                        except asyncio.TimeoutError:
                            print(f"Timeout fetching routes for commodity {commodity_name}")
                            continue
                        except Exception as e:
                            print(f"Error fetching routes for commodity {commodity_name} ({commodity_id}): {e}")
                            continue

                    # Add small delay to avoid rate limiting
                    if i % 5 == 0 and i > 0:
                        await asyncio.sleep(0.3)

            # Sort by profit descending to get the best routes first
            all_routes.sort(key=lambda x: x.get('profit', 0), reverse=True)

            print(f"Found {len(all_routes)} total routes matching criteria")
            return all_routes

        except Exception as e:
            print(f"Error fetching routes for investment {investment}: {e}")
        return []



    @app_commands.command(name="refreshdata", description="Refresh all data from UEX API")
    @app_commands.checks.has_permissions(administrator=True)
    async def refresh_data(self, interaction: discord.Interaction):
        try:
            # Defer the response first
            await interaction.response.defer()

            # Perform the data refresh
            await self.update_data_extract()

            # Get counts for confirmation
            commodities_count = len(self.get_commodities())
            routes_count = len(self.get_routes())
            vehicles_count = len(self.get_vehicles())
            terminals_count = len(self.get_terminals())

            # Send the followup message
            await interaction.followup.send(
                f"✅ Data refreshed successfully from UEX API!\n"
                f"📦 Commodities: {commodities_count}\n"
                f"🚀 Routes: {routes_count}\n"
                f"🚢 Vehicles: {vehicles_count}\n"
                f"🏢 Terminals: {terminals_count}"
            )
        except Exception as e:
            # Handle the case where defer failed
            try:
                await interaction.followup.send(f"❌ Failed to refresh data: {e}")
            except:
                await interaction.response.send_message(f"❌ Failed to refresh data: {e}", ephemeral=True)



async def setup(bot):
    await bot.add_cog(TradeRoutes(bot)) 