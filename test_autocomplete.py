#!/usr/bin/env python3
"""
Test script for the autocomplete functionality
"""

import sys
import os
sys.path.append('.')

from cogs.traderoutes import TradeRoutes

class MockBot:
    pass

def test_autocomplete():
    """Test the autocomplete functionality"""
    print("Testing Autocomplete functionality...")

    # Create a mock bot and trade routes instance without starting the task
    bot = MockBot()
    tr = TradeRoutes.__new__(TradeRoutes)  # Create without calling __init__
    tr.bot = bot
    tr.last_update = None
    
    # Test the autocomplete logic
    print("\n=== Testing Ship Data ===")
    
    ships = tr.parse_vehicles()
    print(f"Total ships loaded: {len(ships)}")
    
    # Filter for cargo ships
    cargo_ships = [s for s in ships if s['scu'] > 0]
    print(f"Ships with cargo capacity: {len(cargo_ships)}")
    
    # Look for 890 Jump specifically
    jump_890 = [s for s in ships if '890' in s['name']]
    print(f"890 Jump ships found: {len(jump_890)}")
    if jump_890:
        for ship in jump_890:
            print(f"  - {ship['name']}: {ship['scu']} SCU, is_cargo: {ship.get('is_cargo', 'N/A')}")
    
    # Test autocomplete with "890"
    print(f"\n=== Testing Autocomplete with '890' ===")
    ship_names = [s['name'] for s in cargo_ships]
    current = "890"
    current_lower = current.lower()
    filtered = [name for name in ship_names if current_lower in name.lower()][:25]
    print(f"Filtered results for '890': {filtered}")
    
    # Test autocomplete with "jump"
    print(f"\n=== Testing Autocomplete with 'jump' ===")
    current = "jump"
    current_lower = current.lower()
    filtered = [name for name in ship_names if current_lower in name.lower()][:25]
    print(f"Filtered results for 'jump': {filtered}")
    
    # Show some example cargo ships
    print(f"\n=== Sample Cargo Ships ===")
    for ship in cargo_ships[:10]:
        print(f"  - {ship['name']}: {ship['scu']} SCU")
    
    print(f"\n=== Large Cargo Ships (>300 SCU) ===")
    large_ships = [s for s in cargo_ships if s['scu'] > 300]
    for ship in large_ships:
        print(f"  - {ship['name']}: {ship['scu']} SCU")

if __name__ == "__main__":
    test_autocomplete()
